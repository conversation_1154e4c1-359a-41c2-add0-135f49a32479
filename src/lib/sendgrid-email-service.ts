import sgMail from '@sendgrid/mail';
import { prisma } from "@/lib/prisma";
import {
  generateWelcomeEmail,
  generateOrderConfirmationEmail,
  generateContactFormEmail,
  generatePasswordResetEmail,
  generateAdminNotificationEmail,
  type WelcomeEmailData,
  type OrderConfirmationEmailData,
  type ContactFormEmailData,
  type PasswordResetEmailData,
  type AdminNotificationEmailData,
} from './email-templates';

interface EmailConfig {
  apiKey: string;
  from: {
    name: string;
    address: string;
  };
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface SendEmailOptions {
  to: string;
  toName?: string;
  subject: string;
  html: string;
  text: string;
  from?: string;
  fromName?: string;
}

export class SendGridEmailService {
  private config: EmailConfig | null = null;
  private initialized = false;

  async initialize(): Promise<boolean> {
    try {
      const apiKey = process.env.SENDGRID_API_KEY;
      
      if (!apiKey) {
        console.warn("SendGrid API key not configured");
        return false;
      }

      this.config = {
        apiKey,
        from: {
          name: process.env.EMAIL_FROM_NAME || "NS Shop",
          address: process.env.EMAIL_FROM || "<EMAIL>",
        },
      };

      // Set the API key
      sgMail.setApiKey(apiKey);
      
      this.initialized = true;
      console.log("SendGrid email service initialized successfully");
      return true;
    } catch (error) {
      console.error("Failed to initialize SendGrid email service:", error);
      return false;
    }
  }

  private async sendEmail(options: SendEmailOptions): Promise<boolean> {
    if (!this.initialized || !this.config) {
      console.warn("SendGrid email service not initialized");
      return false;
    }

    try {
      const msg = {
        to: {
          email: options.to,
          name: options.toName || '',
        },
        from: {
          email: options.from || this.config.from.address,
          name: options.fromName || this.config.from.name,
        },
        subject: options.subject,
        text: options.text,
        html: options.html,
      };

      const result = await sgMail.send(msg);
      console.log("Email sent successfully:", result[0].statusCode);
      return true;
    } catch (error) {
      console.error("Failed to send email:", error);
      return false;
    }
  }

  async sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    const template = generateWelcomeEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendOrderConfirmationEmail(data: OrderConfirmationEmailData): Promise<boolean> {
    const template = generateOrderConfirmationEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendContactFormEmail(data: ContactFormEmailData): Promise<boolean> {
    const template = generateContactFormEmail(data);
    
    // Send to admin/support email
    const adminEmail = process.env.ADMIN_EMAIL || process.env.EMAIL_FROM || "<EMAIL>";
    
    return this.sendEmail({
      to: adminEmail,
      toName: "NS Shop Admin",
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendPasswordResetEmail(data: PasswordResetEmailData): Promise<boolean> {
    const template = generatePasswordResetEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendAdminNotificationEmail(data: AdminNotificationEmailData): Promise<boolean> {
    const template = generateAdminNotificationEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendBulkAdminNotificationEmails(
    notifications: AdminNotificationEmailData[]
  ): Promise<{
    sent: number;
    failed: number;
    errors: string[];
  }> {
    const results = {
      sent: 0,
      failed: 0,
      errors: [] as string[],
    };

    for (const notification of notifications) {
      try {
        const success = await this.sendAdminNotificationEmail(notification);
        if (success) {
          results.sent++;
        } else {
          results.failed++;
          results.errors.push(
            `Failed to send to ${notification.recipientEmail}`
          );
        }
      } catch (error) {
        results.failed++;
        results.errors.push(
          `Error sending to ${notification.recipientEmail}: ${error}`
        );
      }

      // Add small delay to avoid overwhelming SendGrid
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return results;
  }

  async testConnection(): Promise<boolean> {
    if (!this.initialized || !this.config) {
      return false;
    }

    try {
      // SendGrid doesn't have a direct test connection method
      // We'll just verify the API key is set and service is initialized
      return true;
    } catch (error) {
      console.error("SendGrid connection test failed:", error);
      return false;
    }
  }

  async sendTestEmail(recipientEmail: string, recipientName: string = "Test User"): Promise<boolean> {
    const testEmailData: AdminNotificationEmailData = {
      recipientName,
      recipientEmail,
      notification: {
        id: "test",
        title: "Test Email từ NS Shop",
        message: "Đây là email test để kiểm tra cấu hình SendGrid. Nếu bạn nhận được email này, hệ thống email đã hoạt động bình thường.",
        type: "INFO",
        priority: "NORMAL",
        createdAt: new Date().toISOString(),
      },
    };

    return this.sendAdminNotificationEmail(testEmailData);
  }
}

// Singleton instance
export const sendGridEmailService = new SendGridEmailService();

// Helper function to send notification emails using SendGrid
export async function sendNotificationEmails(
  notificationId: string,
  targetAdmins?: string[]
): Promise<boolean> {
  try {
    // Get notification details
    const notification = await prisma.notification.findUnique({
      where: { id: notificationId },
      include: {
        creator: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!notification) {
      console.error("Notification not found:", notificationId);
      return false;
    }

    // Get target admin users
    let adminUsers;
    if (targetAdmins && targetAdmins.length > 0) {
      adminUsers = await prisma.adminUser.findMany({
        where: {
          id: { in: targetAdmins },
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });
    } else {
      // Send to all active admins
      adminUsers = await prisma.adminUser.findMany({
        where: {
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });
    }

    if (adminUsers.length === 0) {
      console.warn("No active admin users found to send notification emails");
      return false;
    }

    // Prepare email data for each admin
    const emailData: AdminNotificationEmailData[] = adminUsers.map((admin) => ({
      recipientName: admin.name,
      recipientEmail: admin.email,
      notification: {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        actionUrl: notification.actionUrl || undefined,
        createdAt: notification.createdAt.toISOString(),
      },
      sender: notification.creator
        ? {
            name: notification.creator.name,
            email: notification.creator.email,
          }
        : undefined,
    }));

    // Initialize email service if not already done
    const initialized = await sendGridEmailService.initialize();
    if (!initialized) {
      console.error("Failed to initialize SendGrid email service");
      return false;
    }

    // Send bulk emails
    const results = await sendGridEmailService.sendBulkAdminNotificationEmails(emailData);
    
    console.log(`Email notification results: ${results.sent} sent, ${results.failed} failed`);
    if (results.errors.length > 0) {
      console.error("Email errors:", results.errors);
    }

    return results.sent > 0;
  } catch (error) {
    console.error("Failed to send notification emails:", error);
    return false;
  }
}
