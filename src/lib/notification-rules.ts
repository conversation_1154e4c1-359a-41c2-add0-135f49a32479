import { prisma } from "@/lib/prisma";
import { sendNotificationEmails } from "@/lib/email-service";
import { NotificationRule, NotificationEvent } from "@/types/notification";

/**
 * Notification Rules Engine
 */
export class NotificationRulesEngine {
  private static instance: NotificationRulesEngine;
  private rules: Map<string, NotificationRule[]> = new Map();
  private initialized = false;

  static getInstance(): NotificationRulesEngine {
    if (!NotificationRulesEngine.instance) {
      NotificationRulesEngine.instance = new NotificationRulesEngine();
    }
    return NotificationRulesEngine.instance;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Load rules from database or initialize default rules
      await this.loadRules();
      this.initialized = true;
      console.log("Notification Rules Engine initialized");
    } catch (error) {
      console.error("Failed to initialize Notification Rules Engine:", error);
    }
  }

  private async loadRules() {
    // For now, we'll use hardcoded rules. In a real implementation,
    // these would be stored in the database
    const defaultRules = this.getDefaultRules();

    // Group rules by event type for efficient lookup
    for (const rule of defaultRules) {
      if (!this.rules.has(rule.eventType)) {
        this.rules.set(rule.eventType, []);
      }
      this.rules.get(rule.eventType)!.push(rule);
    }
  }

  private getDefaultRules(): NotificationRule[] {
    return [
      // Low Stock Alert
      {
        id: "low-stock-alert",
        name: "Low Stock Alert",
        description: "Notify when product stock is low",
        eventType: "product.stock.low",
        conditions: {
          stockLevel: { operator: "<=", value: 10 },
        },
        notificationTemplate: {
          title: "Cảnh báo hết hàng",
          message: "Sản phẩm {{productName}} chỉ còn {{stockLevel}} trong kho",
          type: "WARNING",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          actionUrl: "/admin/products/{{productId}}",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // New Order Notification
      {
        id: "new-order-notification",
        name: "New Order Notification",
        description: "Notify when a new order is placed",
        eventType: "order.created",
        conditions: {},
        notificationTemplate: {
          title: "Đơn hàng mới",
          message:
            "Đơn hàng #{{orderNumber}} từ {{customerName}} - {{totalAmount}}",
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
          actionUrl: "/admin/orders/{{orderId}}",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // High Value Order Alert
      {
        id: "high-value-order-alert",
        name: "High Value Order Alert",
        description: "Notify for high value orders",
        eventType: "order.created",
        conditions: {
          totalAmount: { operator: ">=", value: 5000000 }, // 5M VND
        },
        notificationTemplate: {
          title: "Đơn hàng giá trị cao",
          message:
            "Đơn hàng #{{orderNumber}} có giá trị {{totalAmount}} từ {{customerName}}",
          type: "SUCCESS",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          actionUrl: "/admin/orders/{{orderId}}",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Payment Failed Alert
      {
        id: "payment-failed-alert",
        name: "Payment Failed Alert",
        description: "Notify when payment fails",
        eventType: "payment.failed",
        conditions: {},
        notificationTemplate: {
          title: "Thanh toán thất bại",
          message:
            "Thanh toán cho đơn hàng #{{orderNumber}} đã thất bại - {{reason}}",
          type: "ERROR",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          actionUrl: "/admin/orders/{{orderId}}",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // New User Registration
      {
        id: "new-user-registration",
        name: "New User Registration",
        description: "Notify when a new user registers",
        eventType: "user.registered",
        conditions: {},
        notificationTemplate: {
          title: "Người dùng mới đăng ký",
          message: "{{userName}} ({{userEmail}}) vừa đăng ký tài khoản",
          type: "INFO",
          priority: "LOW",
          targetType: "ALL_ADMINS",
          actionUrl: "/admin/users/{{userId}}",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // System Error Alert
      {
        id: "system-error-alert",
        name: "System Error Alert",
        description: "Notify for system errors",
        eventType: "system.error",
        conditions: {},
        notificationTemplate: {
          title: "Lỗi hệ thống",
          message: "Lỗi hệ thống: {{errorMessage}} tại {{location}}",
          type: "ERROR",
          priority: "URGENT",
          targetType: "ALL_ADMINS",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Product Out of Stock
      {
        id: "product-out-of-stock",
        name: "Product Out of Stock",
        description: "Notify when product is out of stock",
        eventType: "product.stock.empty",
        conditions: {},
        notificationTemplate: {
          title: "Sản phẩm hết hàng",
          message: "Sản phẩm {{productName}} đã hết hàng",
          type: "WARNING",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          actionUrl: "/admin/products/{{productId}}",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // Order Status Changed
      {
        id: "order-status-changed",
        name: "Order Status Changed",
        description: "Notify when order status changes to important states",
        eventType: "order.status.changed",
        conditions: {
          newStatus: {
            operator: "in",
            value: ["CANCELLED", "REFUNDED", "RETURNED"],
          },
        },
        notificationTemplate: {
          title: "Trạng thái đơn hàng thay đổi",
          message:
            "Đơn hàng #{{orderNumber}} đã chuyển sang trạng thái {{newStatus}}",
          type: "WARNING",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
          actionUrl: "/admin/orders/{{orderId}}",
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
  }

  /**
   * Process an event and trigger matching notification rules
   */
  async processEvent(event: NotificationEvent): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    const rules = this.rules.get(event.type) || [];

    for (const rule of rules) {
      if (!rule.isActive) continue;

      try {
        if (this.evaluateConditions(rule.conditions, event.data)) {
          await this.triggerNotification(rule, event);
        }
      } catch (error) {
        console.error(`Error processing rule ${rule.id}:`, error);
      }
    }
  }

  /**
   * Evaluate rule conditions against event data
   */
  private evaluateConditions(
    conditions: Record<string, any>,
    eventData: Record<string, any>
  ): boolean {
    for (const [field, condition] of Object.entries(conditions)) {
      const value = eventData[field];

      if (!this.evaluateCondition(value, condition)) {
        return false;
      }
    }
    return true;
  }

  private evaluateCondition(value: any, condition: any): boolean {
    const { operator, value: conditionValue } = condition;

    switch (operator) {
      case "==":
      case "equals":
        return value === conditionValue;
      case "!=":
      case "not_equals":
        return value !== conditionValue;
      case ">":
      case "greater_than":
        return value > conditionValue;
      case ">=":
      case "greater_than_or_equal":
        return value >= conditionValue;
      case "<":
      case "less_than":
        return value < conditionValue;
      case "<=":
      case "less_than_or_equal":
        return value <= conditionValue;
      case "in":
        return Array.isArray(conditionValue) && conditionValue.includes(value);
      case "not_in":
        return Array.isArray(conditionValue) && !conditionValue.includes(value);
      case "contains":
        return typeof value === "string" && value.includes(conditionValue);
      case "starts_with":
        return typeof value === "string" && value.startsWith(conditionValue);
      case "ends_with":
        return typeof value === "string" && value.endsWith(conditionValue);
      default:
        console.warn(`Unknown condition operator: ${operator}`);
        return false;
    }
  }

  /**
   * Trigger notification based on rule and event
   */
  private async triggerNotification(
    rule: NotificationRule,
    event: NotificationEvent
  ): Promise<void> {
    try {
      // Replace template variables with event data
      const title = this.replaceTemplateVariables(
        rule.notificationTemplate.title,
        event.data
      );
      const message = this.replaceTemplateVariables(
        rule.notificationTemplate.message,
        event.data
      );
      const actionUrl = rule.notificationTemplate.actionUrl
        ? this.replaceTemplateVariables(
            rule.notificationTemplate.actionUrl,
            event.data
          )
        : undefined;

      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          title,
          message,
          type: rule.notificationTemplate.type,
          priority: rule.notificationTemplate.priority,
          targetType: rule.notificationTemplate.targetType,
          targetId: rule.notificationTemplate.targetId,
          actionUrl,
          metadata: {
            ruleId: rule.id,
            eventType: event.type,
            eventData: event.data,
          },
          createdBy: event.triggeredBy || "system",
        },
      });

      console.log(
        `Notification created: ${notification.id} for rule: ${rule.id}`
      );

      // Send email notification if applicable
      try {
        await sendNotificationEmails(notification.id);
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
        // Don't fail the whole process if email fails
      }
    } catch (error) {
      console.error(
        `Failed to trigger notification for rule ${rule.id}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Replace template variables in strings
   */
  private replaceTemplateVariables(
    template: string,
    data: Record<string, any>
  ): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      const value = data[key];
      if (value === undefined || value === null) {
        return match; // Keep the placeholder if no value found
      }
      return String(value);
    });
  }

  /**
   * Add or update a rule
   */
  async addRule(
    rule: Omit<NotificationRule, "id" | "createdAt" | "updatedAt">
  ): Promise<NotificationRule> {
    const newRule: NotificationRule = {
      ...rule,
      id: `custom-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    if (!this.rules.has(rule.eventType)) {
      this.rules.set(rule.eventType, []);
    }
    this.rules.get(rule.eventType)!.push(newRule);

    return newRule;
  }

  /**
   * Get all rules for an event type
   */
  getRulesForEvent(eventType: string): NotificationRule[] {
    return this.rules.get(eventType) || [];
  }

  /**
   * Get all rules
   */
  getAllRules(): NotificationRule[] {
    const allRules: NotificationRule[] = [];
    for (const rules of this.rules.values()) {
      allRules.push(...rules);
    }
    return allRules;
  }
}

// Singleton instance
export const notificationRulesEngine = NotificationRulesEngine.getInstance();

// Helper functions for common events

export async function triggerLowStockAlert(
  productId: string,
  productName: string,
  stockLevel: number
) {
  await notificationRulesEngine.processEvent({
    type: "product.stock.low",
    data: {
      productId,
      productName,
      stockLevel,
    },
    timestamp: new Date(),
  });
}

export async function triggerNewOrderNotification(
  orderId: string,
  orderNumber: string,
  customerName: string,
  totalAmount: number
) {
  await notificationRulesEngine.processEvent({
    type: "order.created",
    data: {
      orderId,
      orderNumber,
      customerName,
      totalAmount,
    },
    timestamp: new Date(),
  });
}

export async function triggerPaymentFailedAlert(
  orderId: string,
  orderNumber: string,
  reason: string
) {
  await notificationRulesEngine.processEvent({
    type: "payment.failed",
    data: {
      orderId,
      orderNumber,
      reason,
    },
    timestamp: new Date(),
  });
}

export async function triggerUserRegistrationNotification(
  userId: string,
  userName: string,
  userEmail: string
) {
  await notificationRulesEngine.processEvent({
    type: "user.registered",
    data: {
      userId,
      userName,
      userEmail,
    },
    timestamp: new Date(),
  });
}

export async function triggerSystemErrorAlert(
  errorMessage: string,
  location: string
) {
  await notificationRulesEngine.processEvent({
    type: "system.error",
    data: {
      errorMessage,
      location,
    },
    timestamp: new Date(),
  });
}

export async function triggerOrderStatusChanged(
  orderId: string,
  orderNumber: string,
  oldStatus: string,
  newStatus: string
) {
  await notificationRulesEngine.processEvent({
    type: "order.status.changed",
    data: {
      orderId,
      orderNumber,
      oldStatus,
      newStatus,
    },
    timestamp: new Date(),
  });
}
