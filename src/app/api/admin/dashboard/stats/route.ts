import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Calculate date ranges
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get audit logs statistics
    const [
      totalAuditLogs,
      todayAuditLogs,
      thisWeekAuditLogs,
      totalNotifications,
      unreadNotifications,
      urgentNotifications,
      uniqueAdminsToday,
      topActionsToday,
    ] = await Promise.all([
      // Audit logs stats
      prisma.auditLog.count(),
      prisma.auditLog.count({
        where: {
          createdAt: { gte: today },
        },
      }),
      prisma.auditLog.count({
        where: {
          createdAt: { gte: thisWeek },
        },
      }),

      // Notifications stats
      prisma.notification.count(),
      prisma.notification.count({
        where: { isRead: false },
      }),
      prisma.notification.count({
        where: {
          priority: "URGENT",
          isRead: false,
        },
      }),

      // Activity stats
      prisma.auditLog.findMany({
        where: {
          createdAt: { gte: today },
        },
        select: { adminId: true },
        distinct: ["adminId"],
      }),

      // Top actions today
      prisma.auditLog.groupBy({
        by: ["action"],
        where: {
          createdAt: { gte: today },
        },
        _count: {
          action: true,
        },
        orderBy: {
          _count: {
            action: "desc",
          },
        },
        take: 5,
      }),
    ]);

    // Get system health (simplified)
    const systemHealth = await getSystemHealth();

    // Format response
    const stats = {
      auditLogs: {
        total: totalAuditLogs,
        today: todayAuditLogs,
        thisWeek: thisWeekAuditLogs,
      },
      notifications: {
        total: totalNotifications,
        unread: unreadNotifications,
        urgent: urgentNotifications,
      },
      systemHealth,
      recentActivity: {
        totalActions: todayAuditLogs,
        uniqueAdmins: uniqueAdminsToday.length,
        topActions: topActionsToday.map(item => ({
          action: item.action,
          count: item._count.action,
        })),
      },
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function getSystemHealth() {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    // Get uptime (simplified - in a real app, you'd track this properly)
    const uptimeMs = process.uptime() * 1000;
    const uptimeHours = Math.floor(uptimeMs / (1000 * 60 * 60));
    const uptimeMinutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
    
    // Check for recent errors
    const recentErrors = await prisma.auditLog.count({
      where: {
        action: { contains: "FAILED" },
        createdAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
        },
      },
    });

    // Determine system status
    let status: "healthy" | "warning" | "critical" = "healthy";
    
    if (recentErrors > 10) {
      status = "critical";
    } else if (recentErrors > 5) {
      status = "warning";
    }

    return {
      status,
      uptime: `${uptimeHours}h ${uptimeMinutes}m`,
      lastCheck: new Date().toISOString(),
      recentErrors,
    };
  } catch (error) {
    console.error("Error checking system health:", error);
    return {
      status: "critical" as const,
      uptime: "Unknown",
      lastCheck: new Date().toISOString(),
      recentErrors: 0,
    };
  }
}
