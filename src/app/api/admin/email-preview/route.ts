import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import {
  generateWelcomeEmail,
  generateOrderConfirmationEmail,
  generateContactFormEmail,
  generatePasswordResetEmail,
  generateAdminNotificationEmail,
  type WelcomeEmailData,
  type OrderConfirmationEmailData,
  type ContactFormEmailData,
  type PasswordResetEmailData,
  type AdminNotificationEmailData,
} from "@/lib/email/templates";

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const template = searchParams.get("template");
    const format = searchParams.get("format") || "html"; // html or text

    if (!template) {
      return NextResponse.json(
        { error: "Template parameter is required" },
        { status: 400 }
      );
    }

    let emailResult;

    switch (template) {
      case "welcome":
        const welcomeData: WelcomeEmailData = {
          recipientName: "Nguyễn Văn A",
          recipientEmail: "<EMAIL>",
          loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/auth/signin`,
          supportEmail: "<EMAIL>",
        };
        emailResult = generateWelcomeEmail(welcomeData);
        break;

      case "order-confirmation":
        const orderData: OrderConfirmationEmailData = {
          recipientName: "Nguyễn Văn A",
          recipientEmail: "<EMAIL>",
          order: {
            id: "order_1234567890abcdef",
            total: 1250000,
            status: "PENDING",
            createdAt: new Date().toISOString(),
            items: [
              {
                name: "Áo thun nam basic",
                quantity: 2,
                price: 299000,
                image: "https://via.placeholder.com/300x300?text=Áo+thun",
              },
              {
                name: "Quần jean slim fit",
                quantity: 1,
                price: 650000,
                image: "https://via.placeholder.com/300x300?text=Quần+jean",
              },
            ],
            shippingAddress: {
              fullName: "Nguyễn Văn A",
              address: "123 Đường Lê Lợi, Phường Bến Nghé",
              city: "Quận 1, TP. Hồ Chí Minh",
              postalCode: "700000",
              phone: "0123456789",
            },
          },
          trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/orders/order_1234567890abcdef`,
        };
        emailResult = generateOrderConfirmationEmail(orderData);
        break;

      case "contact-form":
        const contactData: ContactFormEmailData = {
          recipientName: "NS Shop Admin",
          recipientEmail: "<EMAIL>",
          senderName: "Nguyễn Thị B",
          senderEmail: "<EMAIL>",
          senderPhone: "0987654321",
          subject: "Hỏi về chính sách đổi trả",
          message:
            "Xin chào,\n\nTôi muốn hỏi về chính sách đổi trả sản phẩm. Cụ thể là tôi đã mua một chiếc áo nhưng size không vừa. Tôi có thể đổi sang size khác được không?\n\nCảm ơn shop!",
          submittedAt: new Date().toISOString(),
        };
        emailResult = generateContactFormEmail(contactData);
        break;

      case "password-reset":
        const resetData: PasswordResetEmailData = {
          recipientName: "Nguyễn Văn A",
          recipientEmail: "<EMAIL>",
          resetUrl: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password?token=sample_reset_token_123456789`,
          expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
        };
        emailResult = generatePasswordResetEmail(resetData);
        break;

      case "admin-notification":
        const notificationData: AdminNotificationEmailData = {
          recipientName: "Admin User",
          recipientEmail: "<EMAIL>",
          notification: {
            id: "notif_123456789",
            title: "Đơn hàng mới cần xử lý",
            message:
              "Có đơn hàng mới #12345 từ khách hàng Nguyễn Văn A với tổng giá trị 1.250.000 VNĐ cần được xử lý.\n\nThông tin khách hàng:\n- Tên: Nguyễn Văn A\n- Email: <EMAIL>\n- Số điện thoại: 0123456789\n\nVui lòng kiểm tra và xử lý đơn hàng trong thời gian sớm nhất.",
            type: "INFO",
            priority: "HIGH",
            actionUrl: `${process.env.NEXT_PUBLIC_APP_URL}/admin/orders/order_1234567890abcdef`,
            createdAt: new Date().toISOString(),
          },
          sender: {
            name: "Hệ thống NS Shop",
            email: "<EMAIL>",
          },
        };
        emailResult = generateAdminNotificationEmail(notificationData);
        break;

      default:
        return NextResponse.json(
          {
            error:
              "Invalid template. Available: welcome, order-confirmation, contact-form, password-reset, admin-notification",
          },
          { status: 400 }
        );
    }

    if (format === "text") {
      return new NextResponse(emailResult.text, {
        headers: {
          "Content-Type": "text/plain; charset=utf-8",
        },
      });
    }

    // Return HTML by default
    return new NextResponse(emailResult.html, {
      headers: {
        "Content-Type": "text/html; charset=utf-8",
      },
    });
  } catch (error) {
    console.error("Email preview error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET endpoint to list available templates
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const templates = [
      {
        id: "welcome",
        name: "Welcome Email",
        description: "Email gửi cho khách hàng mới đăng ký",
        previewUrl: "/api/admin/email-preview?template=welcome",
      },
      {
        id: "order-confirmation",
        name: "Order Confirmation",
        description: "Email xác nhận đơn hàng",
        previewUrl: "/api/admin/email-preview?template=order-confirmation",
      },
      {
        id: "contact-form",
        name: "Contact Form",
        description: "Email từ form liên hệ gửi cho admin",
        previewUrl: "/api/admin/email-preview?template=contact-form",
      },
      {
        id: "password-reset",
        name: "Password Reset",
        description: "Email đặt lại mật khẩu",
        previewUrl: "/api/admin/email-preview?template=password-reset",
      },
      {
        id: "admin-notification",
        name: "Admin Notification",
        description: "Email thông báo cho admin",
        previewUrl: "/api/admin/email-preview?template=admin-notification",
      },
    ];

    return NextResponse.json({
      templates,
      baseUrl: process.env.NEXT_PUBLIC_APP_URL,
    });
  } catch (error) {
    console.error("Email templates list error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
