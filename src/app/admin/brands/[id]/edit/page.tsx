"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Save, Star, Globe, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { MediaSelector } from "@/components/admin/MediaManager";
import { toast } from "sonner";

interface BrandFormData {
  name: string;
  description: string;
  slug: string;
  logo: string;
  website: string;
  isActive: boolean;
}

interface Brand {
  id: string;
  name: string;
  description?: string;
  slug: string;
  logo?: string;
  website?: string;
  isActive: boolean;
  _count: {
    products: number;
  };
}

export default function EditBrandPage() {
  const router = useRouter();
  const params = useParams();
  const brandId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [formData, setFormData] = useState<BrandFormData>({
    name: "",
    description: "",
    slug: "",
    logo: "",
    website: "",
    isActive: true,
  });

  // Load brand data
  useEffect(() => {
    if (brandId) {
      loadBrand();
    }
  }, [brandId]);

  const loadBrand = async () => {
    try {
      const response = await fetch(`/api/admin/brands/${brandId}`);
      const data = await response.json();

      if (response.ok && data.success) {
        const brandData = data.data;
        setBrand(brandData);
        setFormData({
          name: brandData.name,
          description: brandData.description || "",
          slug: brandData.slug,
          logo: brandData.logo || "",
          website: brandData.website || "",
          isActive: brandData.isActive,
        });
      } else {
        toast.error("Không tìm thấy thương hiệu");
        router.push("/admin/brands");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tải thông tin thương hiệu");
      router.push("/admin/brands");
    } finally {
      setInitialLoading(false);
    }
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  };

  // Handle form input changes
  const handleInputChange = (field: keyof BrandFormData, value: string | boolean) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Auto-generate slug when name changes (but allow manual editing)
      if (field === "name" && typeof value === "string" && prev.slug === generateSlug(prev.name)) {
        updated.slug = generateSlug(value);
      }
      
      return updated;
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name.trim()) {
      toast.error("Tên thương hiệu là bắt buộc");
      return;
    }
    
    if (!formData.slug.trim()) {
      toast.error("Slug là bắt buộc");
      return;
    }

    if (formData.website && !formData.website.startsWith("http")) {
      toast.error("Website phải bắt đầu bằng http:// hoặc https://");
      return;
    }

    setLoading(true);
    
    try {
      const response = await fetch(`/api/admin/brands/${brandId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        toast.success("Thương hiệu đã được cập nhật thành công");
        router.push("/admin/brands");
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi cập nhật thương hiệu");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật thương hiệu");
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Đang tải thông tin thương hiệu...</span>
        </div>
      </div>
    );
  }

  if (!brand) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Không tìm thấy thương hiệu</h2>
          <p className="text-muted-foreground mb-4">
            Thương hiệu bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
          </p>
          <Link href="/admin/brands">
            <Button>Quay lại danh sách thương hiệu</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/brands">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>
        
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <Star className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa thương hiệu</h1>
            <p className="text-muted-foreground">
              Cập nhật thông tin thương hiệu "{brand.name}"
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Thông tin cơ bản</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Tên thương hiệu *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Nhập tên thương hiệu"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="slug">Slug *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => handleInputChange("slug", e.target.value)}
                    placeholder="slug-thuong-hieu"
                    required
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    URL thân thiện cho thương hiệu (tự động tạo từ tên)
                  </p>
                </div>

                <div>
                  <Label htmlFor="description">Mô tả</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Mô tả về thương hiệu..."
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="website">Website</Label>
                  <div className="relative">
                    <Globe className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="website"
                      type="url"
                      value={formData.website}
                      onChange={(e) => handleInputChange("website", e.target.value)}
                      placeholder="https://example.com"
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="logo">Logo thương hiệu</Label>
                  <MediaSelector
                    value={formData.logo}
                    onChange={(url) => handleInputChange("logo", url)}
                    folder="brands"
                    placeholder="Chọn logo thương hiệu..."
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Trạng thái</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="isActive">Kích hoạt</Label>
                    <p className="text-sm text-muted-foreground">
                      Thương hiệu có hiển thị trên website không
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange("isActive", checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            {formData.logo && (
              <Card>
                <CardHeader>
                  <CardTitle>Xem trước logo</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
                    <img
                      src={formData.logo}
                      alt="Logo preview"
                      className="max-w-full max-h-32 object-contain"
                      onError={(e) => {
                        e.currentTarget.style.display = "none";
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Thống kê</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Số sản phẩm:</span>
                    <span className="font-medium">{brand._count.products}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-4">
          <Button
            type="submit"
            disabled={loading}
            className="bg-pink-600 hover:bg-pink-700"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Cập nhật thương hiệu
          </Button>
          <Link href="/admin/brands">
            <Button type="button" variant="outline">
              Hủy
            </Button>
          </Link>
        </div>
      </form>
    </div>
  );
}
