"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { PostForm } from "@/components/admin/PostForm";
import { Button } from "@/components/ui/button";
import { ArrowLeft, FileText, Loader2 } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

interface PostFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  categoryId?: string;
}

interface Post extends PostFormData {
  id: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  category?: {
    id: string;
    name: string;
    slug: string;
  };
}

interface EditPostPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditPostPage({ params }: EditPostPageProps) {
  const resolvedParams = use(params);
  const router = useRouter();
  const [post, setPost] = useState<Post | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingPost, setIsLoadingPost] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load post data
  useEffect(() => {
    const fetchPost = async () => {
      try {
        const response = await fetch(`/api/admin/posts/${resolvedParams.id}`);
        const result = await response.json();

        if (response.ok && result.success) {
          setPost(result.data);
        } else {
          setError(result.error || "Không tìm thấy bài viết");
        }
      } catch (error) {
        console.error("Fetch post error:", error);
        setError("Có lỗi xảy ra khi tải bài viết");
      } finally {
        setIsLoadingPost(false);
      }
    };

    if (resolvedParams.id) {
      fetchPost();
    }
  }, [resolvedParams.id]);

  const handleSubmit = async (data: PostFormData) => {
    if (!post) return;

    setIsLoading(true);

    try {
      const response = await fetch(`/api/admin/posts/${post.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || "Cập nhật bài viết thành công");

        // Update local state
        setPost({ ...post, ...data });

        // Optionally redirect back to posts list
        // router.push('/admin/posts');
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi cập nhật bài viết");
      }
    } catch (error) {
      console.error("Update post error:", error);
      toast.error("Có lỗi xảy ra khi cập nhật bài viết");
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state
  if (isLoadingPost) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/admin/posts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>

          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
              <Loader2 className="h-5 w-5 text-pink-600 animate-spin" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Đang tải...</h1>
              <p className="text-muted-foreground">
                Đang tải thông tin bài viết
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !post) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/admin/posts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>

          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Lỗi</h1>
              <p className="text-muted-foreground">
                {error || "Không tìm thấy bài viết"}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/posts">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>

        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <FileText className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa bài viết</h1>
            <p className="text-muted-foreground">{post.title}</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <PostForm
        initialData={{
          id: post.id,
          title: post.title,
          content: post.content,
          excerpt: post.excerpt,
          slug: post.slug,
          status: post.status,
          featured: post.featured,
          featuredImage: post.featuredImage,
          tags: post.tags,
          categoryId: post.categoryId,
        }}
        onSubmit={handleSubmit}
        isLoading={isLoading}
      />
    </div>
  );
}
