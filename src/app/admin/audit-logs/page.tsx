import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { adminAuthOptions } from "@/lib/admin-auth";
import { AuditLogsList } from "@/components/admin/audit-logs/AuditLogsList";
import { prisma } from "@/lib/prisma";

export const metadata: Metadata = {
  title: "Audit Logs - NS Shop Admin",
  description: "Theo <PERSON> và quản lý tất cả hoạt động của admin trong hệ thống",
};

interface SearchParams {
  page?: string;
  limit?: string;
  action?: string;
  resource?: string;
  adminId?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: string;
}

interface AuditLogsPageProps {
  searchParams: SearchParams;
}

export default async function AuditLogsPage({ searchParams }: AuditLogsPageProps) {
  // Check admin authentication
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    redirect("/admin/auth/signin");
  }

  // Parse search parameters
  const page = parseInt(searchParams.page || "1");
  const limit = parseInt(searchParams.limit || "20");
  const {
    action,
    resource,
    adminId,
    startDate,
    endDate,
    search,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = searchParams;

  // Build where clause
  const where: any = {};

  if (action) {
    where.action = { contains: action, mode: "insensitive" };
  }

  if (resource) {
    where.resource = { contains: resource, mode: "insensitive" };
  }

  if (adminId) {
    where.adminId = adminId;
  }

  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) {
      where.createdAt.gte = new Date(startDate);
    }
    if (endDate) {
      where.createdAt.lte = new Date(endDate);
    }
  }

  if (search) {
    where.OR = [
      { action: { contains: search, mode: "insensitive" } },
      { resource: { contains: search, mode: "insensitive" } },
      { description: { contains: search, mode: "insensitive" } },
      { admin: { name: { contains: search, mode: "insensitive" } } },
    ];
  }

  // Build orderBy clause
  let orderBy: any = {};
  if (sortBy === "admin") {
    orderBy = { admin: { name: sortOrder } };
  } else {
    orderBy[sortBy] = sortOrder;
  }

  try {
    // Get total count for pagination
    const totalCount = await prisma.auditLog.count({ where });

    // Get audit logs with admin information
    const auditLogs = await prisma.auditLog.findMany({
      where,
      include: {
        admin: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            role: true,
          },
        },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Prepare initial data for client component
    const initialData = {
      data: auditLogs.map(log => ({
        ...log,
        createdAt: log.createdAt.toISOString(),
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
      filters: {
        action,
        resource,
        adminId,
        startDate,
        endDate,
        search,
        sortBy,
        sortOrder,
      },
    };

    return (
      <div className="container mx-auto py-6">
        <AuditLogsList initialData={initialData} />
      </div>
    );
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">
            Lỗi tải dữ liệu
          </h1>
          <p className="text-muted-foreground mb-4">
            Không thể tải danh sách audit logs. Vui lòng thử lại sau.
          </p>
          <AuditLogsList />
        </div>
      </div>
    );
  }
}
