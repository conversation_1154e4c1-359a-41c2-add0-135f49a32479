"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Save, Eye, X, Plus, Tag as TagIcon } from "lucide-react";
import { MediaSelector } from "./MediaManager";
import { RichTextEditor } from "@/components/ui/rich-text-editor";

// Form validation schema
const postFormSchema = z.object({
  title: z.string().min(1, "Tiêu đề là bắt buộc"),
  content: z.string().min(1, "Nội dung là bắt buộc"),
  excerpt: z.string().optional(),
  slug: z.string().optional(),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).default("DRAFT"),
  featured: z.boolean().default(false),
  featuredImage: z.string().optional(),
  tags: z.array(z.string()).default([]),
  categoryId: z.string().nullable().optional(),
});

type PostFormData = z.infer<typeof postFormSchema>;

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface PostFormProps {
  initialData?: Partial<PostFormData> & { id?: string };
  onSubmit: (data: PostFormData) => Promise<void>;
  isLoading?: boolean;
}

export function PostForm({
  initialData,
  onSubmit,
  isLoading = false,
}: PostFormProps) {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [tagInput, setTagInput] = useState("");

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset,
  } = useForm<PostFormData>({
    resolver: zodResolver(postFormSchema),
    defaultValues: {
      title: "",
      content: "",
      excerpt: "",
      slug: "",
      status: "DRAFT",
      featured: false,
      featuredImage: "",
      tags: [],
      ...initialData,
      // Handle null categoryId from initialData
      categoryId: initialData?.categoryId || "none",
    },
  });

  const watchedTitle = watch("title");
  const watchedContent = watch("content");
  const watchedTags = watch("tags");
  const watchedFeaturedImage = watch("featuredImage");

  // Load categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  // Auto-generate slug from title
  useEffect(() => {
    if (watchedTitle && !initialData?.slug) {
      const slug = watchedTitle
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setValue("slug", slug);
    }
  }, [watchedTitle, setValue, initialData?.slug]);

  // Auto-generate excerpt from content
  useEffect(() => {
    if (watchedContent && !watch("excerpt")) {
      const excerpt = watchedContent
        .replace(/<[^>]*>/g, "") // Remove HTML tags
        .substring(0, 200)
        .trim();
      if (excerpt.length === 200) {
        setValue("excerpt", excerpt + "...");
      } else {
        setValue("excerpt", excerpt);
      }
    }
  }, [watchedContent, setValue, watch]);

  // Handle tag input
  const handleAddTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      setValue("tags", [...watchedTags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setValue(
      "tags",
      watchedTags.filter((tag) => tag !== tagToRemove)
    );
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle form submission
  const onFormSubmit = async (data: PostFormData) => {
    try {
      // Convert "none" categoryId to null
      const processedData = {
        ...data,
        categoryId: data.categoryId === "none" ? null : data.categoryId,
      };
      await onSubmit(processedData);
      if (!initialData?.id) {
        reset();
        setTagInput("");
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  // Handle save as draft
  const handleSaveAsDraft = () => {
    setValue("status", "DRAFT");
    handleSubmit(onFormSubmit)();
  };

  // Handle publish
  const handlePublish = () => {
    setValue("status", "PUBLISHED");
    handleSubmit(onFormSubmit)();
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Title */}
          <Card>
            <CardHeader>
              <CardTitle>Thông tin cơ bản</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Tiêu đề *</Label>
                <Input
                  id="title"
                  {...register("title")}
                  placeholder="Nhập tiêu đề bài viết..."
                  className={errors.title ? "border-red-500" : ""}
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.title.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  {...register("slug")}
                  placeholder="slug-bai-viet"
                />
                {errors.slug && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.slug.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="excerpt">Tóm tắt</Label>
                <Textarea
                  id="excerpt"
                  {...register("excerpt")}
                  placeholder="Tóm tắt ngắn gọn về bài viết..."
                  rows={3}
                />
                {errors.excerpt && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.excerpt.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Content Editor */}
          <Card>
            <CardHeader>
              <CardTitle>Nội dung</CardTitle>
            </CardHeader>
            <CardContent>
              <RichTextEditor
                content={watch("content") || ""}
                onChange={(content) => setValue("content", content)}
                placeholder="Viết nội dung bài viết của bạn..."
              />
              {errors.content && (
                <p className="text-sm text-red-500 mt-2">
                  {errors.content.message}
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Hành động</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                type="button"
                onClick={handleSaveAsDraft}
                variant="outline"
                className="w-full"
                disabled={isLoading}
              >
                <Save className="h-4 w-4 mr-2" />
                Lưu bản nháp
              </Button>

              <Button
                type="button"
                onClick={handlePublish}
                className="w-full bg-pink-600 hover:bg-pink-700"
                disabled={isLoading}
              >
                <Eye className="h-4 w-4 mr-2" />
                Xuất bản
              </Button>

              {initialData?.id && (
                <Button
                  type="button"
                  variant="ghost"
                  className="w-full"
                  onClick={() => router.push(`/admin/posts`)}
                >
                  Hủy
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Cài đặt</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Status */}
              <div>
                <Label htmlFor="status">Trạng thái</Label>
                <Select
                  value={watch("status")}
                  onValueChange={(value) => setValue("status", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DRAFT">Bản nháp</SelectItem>
                    <SelectItem value="PUBLISHED">Đã xuất bản</SelectItem>
                    <SelectItem value="ARCHIVED">Đã lưu trữ</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Featured */}
              <div className="flex items-center justify-between">
                <Label htmlFor="featured">Bài viết nổi bật</Label>
                <Switch
                  id="featured"
                  checked={watch("featured")}
                  onCheckedChange={(checked) => setValue("featured", checked)}
                />
              </div>

              {/* Category */}
              <div>
                <Label htmlFor="categoryId">Danh mục</Label>
                <Select
                  value={watch("categoryId") || "none"}
                  onValueChange={(value) => setValue("categoryId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn danh mục" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Không có danh mục</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader>
              <CardTitle>Ảnh đại diện</CardTitle>
            </CardHeader>
            <CardContent>
              <MediaSelector
                value={watchedFeaturedImage}
                onChange={(url) => setValue("featuredImage", url)}
                folder="posts"
                placeholder="Chọn ảnh đại diện..."
              />
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle>Thẻ</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex gap-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleTagKeyPress}
                  placeholder="Nhập thẻ..."
                  className="flex-1"
                />
                <Button
                  type="button"
                  onClick={handleAddTag}
                  size="sm"
                  disabled={!tagInput.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {watchedTags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {watchedTags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      <TagIcon className="h-3 w-3" />
                      {tag}
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}

              {watchedTags.length === 0 && (
                <p className="text-sm text-gray-500">
                  Chưa có thẻ nào. Thêm thẻ để giúp người đọc tìm thấy bài viết
                  dễ dàng hơn.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </form>
  );
}
