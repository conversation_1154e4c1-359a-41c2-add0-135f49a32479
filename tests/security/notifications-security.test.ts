/**
 * Notifications Security Tests
 * <PERSON><PERSON><PERSON> tra bảo mật cho hệ thống notifications
 */

import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/notifications/route";
import { GET as getById, PUT, DELETE } from "@/app/api/admin/notifications/[id]/route";
import { POST as bulkOperations } from "@/app/api/admin/notifications/bulk/route";
import { getServerSession } from "next-auth";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Notifications Security Tests", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      type: "admin",
      role: "ADMIN",
      name: "Test Admin",
    },
  };

  const mockModeratorSession = {
    user: {
      id: "moderator-123",
      type: "admin",
      role: "MODERATOR",
      name: "Test Moderator",
    },
  };

  const mockUserSession = {
    user: {
      id: "user-123",
      type: "user",
      role: "USER",
      name: "Regular User",
    },
  };

  beforeEach(async () => {
    // Clean up test data
    await prisma.notification.deleteMany();
    await prisma.adminUser.deleteMany();

    // Create test admin users
    await prisma.adminUser.createMany({
      data: [
        {
          id: "admin-123",
          email: "<EMAIL>",
          name: "Test Admin",
          password: "hashed-password",
          role: "ADMIN",
        },
        {
          id: "moderator-123",
          email: "<EMAIL>",
          name: "Test Moderator",
          password: "hashed-password",
          role: "MODERATOR",
        },
        {
          id: "admin-456",
          email: "<EMAIL>",
          name: "Test Admin 2",
          password: "hashed-password",
          role: "ADMIN",
        },
      ],
    });
  });

  afterAll(async () => {
    await prisma.notification.deleteMany();
    await prisma.adminUser.deleteMany();
    await prisma.$disconnect();
  });

  describe("Authentication and Authorization", () => {
    it("should deny access to unauthenticated users", async () => {
      mockGetServerSession.mockResolvedValueOnce(null);

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");
      const response = await GET(request);

      expect(response.status).toBe(403);
    });

    it("should deny access to regular users", async () => {
      mockGetServerSession.mockResolvedValueOnce(mockUserSession);

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");
      const response = await GET(request);

      expect(response.status).toBe(403);
    });

    it("should allow access to admin users", async () => {
      mockGetServerSession.mockResolvedValueOnce(mockAdminSession);

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");
      const response = await GET(request);

      expect(response.status).toBe(200);
    });

    it("should allow access to moderator users", async () => {
      mockGetServerSession.mockResolvedValueOnce(mockModeratorSession);

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");
      const response = await GET(request);

      expect(response.status).toBe(200);
    });
  });

  describe("Notification Targeting Security", () => {
    it("should only show notifications targeted to current user", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create notifications with different targeting
      await prisma.notification.createMany({
        data: [
          {
            title: "All Admins Notification",
            message: "For all admins",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ALL_ADMINS",
            createdBy: "admin-456",
          },
          {
            title: "Specific Admin Notification",
            message: "For admin-123 only",
            type: "INFO",
            priority: "NORMAL",
            targetType: "SPECIFIC_ADMIN",
            targetId: "admin-123",
            createdBy: "admin-456",
          },
          {
            title: "Other Admin Notification",
            message: "For admin-456 only",
            type: "INFO",
            priority: "NORMAL",
            targetType: "SPECIFIC_ADMIN",
            targetId: "admin-456",
            createdBy: "admin-456",
          },
          {
            title: "Role Admin Notification",
            message: "For admin role",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ROLE_ADMIN",
            createdBy: "admin-456",
          },
        ],
      });

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");
      const response = await GET(request);

      expect(response.status).toBe(200);

      const data = await response.json();
      
      // Should only see notifications targeted to admin-123
      const titles = data.notifications.map((n: any) => n.title);
      expect(titles).toContain("All Admins Notification");
      expect(titles).toContain("Specific Admin Notification");
      expect(titles).toContain("Role Admin Notification");
      expect(titles).not.toContain("Other Admin Notification");
    });

    it("should prevent access to notifications not targeted to user", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create notification for different admin
      const notification = await prisma.notification.create({
        data: {
          title: "Private Notification",
          message: "For admin-456 only",
          type: "INFO",
          priority: "NORMAL",
          targetType: "SPECIFIC_ADMIN",
          targetId: "admin-456",
          createdBy: "admin-456",
        },
      });

      // Try to access as admin-123
      const request = new NextRequest(`http://localhost:3000/api/admin/notifications/${notification.id}`);
      const response = await getById(request, { params: { id: notification.id } });

      // Should not allow access to notification not targeted to current user
      expect([403, 404]).toContain(response.status);
    });

    it("should prevent unauthorized modification of notifications", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      // Create notification as admin
      const notification = await prisma.notification.create({
        data: {
          title: "Admin Notification",
          message: "Created by admin",
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
          createdBy: "admin-123",
        },
      });

      // Try to modify as moderator
      const request = new NextRequest(`http://localhost:3000/api/admin/notifications/${notification.id}`, {
        method: "PUT",
        body: JSON.stringify({ isRead: true }),
        headers: { "content-type": "application/json" },
      });

      const response = await PUT(request, { params: { id: notification.id } });

      // Moderators should be able to mark as read but not modify other properties
      expect([200, 403]).toContain(response.status);
    });
  });

  describe("Input Validation and Sanitization", () => {
    it("should validate and sanitize notification creation input", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const maliciousData = {
        title: "<script>alert('xss')</script>",
        message: "<img src=x onerror=alert('xss')>",
        type: "INFO",
        priority: "NORMAL",
        targetType: "ALL_ADMINS",
        actionUrl: "javascript:alert('xss')",
        metadata: {
          malicious: "<script>document.cookie</script>",
        },
      };

      const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
        method: "POST",
        body: JSON.stringify(maliciousData),
        headers: { "content-type": "application/json" },
      });

      const response = await POST(request);

      if (response.status === 201) {
        const data = await response.json();
        
        // Verify malicious content is sanitized
        expect(data.notification.title).not.toContain("<script>");
        expect(data.notification.message).not.toContain("<img");
        expect(data.notification.actionUrl).not.toMatch(/^javascript:/);
      } else {
        // Should reject malicious input
        expect([400, 422]).toContain(response.status);
      }
    });

    it("should validate notification data types and constraints", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const invalidData = [
        {
          // Missing required fields
          type: "INFO",
          priority: "NORMAL",
        },
        {
          title: "Test",
          message: "Test",
          type: "INVALID_TYPE", // Invalid enum
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
        },
        {
          title: "Test",
          message: "Test",
          type: "INFO",
          priority: "INVALID_PRIORITY", // Invalid enum
          targetType: "ALL_ADMINS",
        },
        {
          title: "", // Empty title
          message: "Test",
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
        },
      ];

      for (const data of invalidData) {
        const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
          method: "POST",
          body: JSON.stringify(data),
          headers: { "content-type": "application/json" },
        });

        const response = await POST(request);

        // Should reject invalid data
        expect([400, 422]).toContain(response.status);
      }
    });

    it("should prevent SQL injection in notification queries", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const maliciousQueries = [
        "?type=' OR '1'='1",
        "?priority=HIGH'; DROP TABLE notifications; --",
        "?unreadOnly=true'; DELETE FROM admin_users; --",
      ];

      for (const query of maliciousQueries) {
        const request = new NextRequest(`http://localhost:3000/api/admin/notifications${query}`);
        const response = await GET(request);

        // Should handle malicious queries safely
        expect([200, 400, 422]).toContain(response.status);

        if (response.status === 200) {
          const data = await response.json();
          expect(data).toHaveProperty("notifications");
          expect(Array.isArray(data.notifications)).toBe(true);
        }
      }
    });
  });

  describe("Bulk Operations Security", () => {
    it("should validate bulk operation permissions", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      // Create notifications
      const notifications = await Promise.all([
        prisma.notification.create({
          data: {
            title: "Test 1",
            message: "Message 1",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ALL_ADMINS",
            createdBy: "admin-123",
          },
        }),
        prisma.notification.create({
          data: {
            title: "Test 2",
            message: "Message 2",
            type: "INFO",
            priority: "NORMAL",
            targetType: "SPECIFIC_ADMIN",
            targetId: "admin-456", // Not targeted to moderator
            createdBy: "admin-123",
          },
        }),
      ]);

      // Try bulk operation including notification not targeted to user
      const request = new NextRequest("http://localhost:3000/api/admin/notifications/bulk", {
        method: "POST",
        body: JSON.stringify({
          action: "markAsRead",
          notificationIds: notifications.map(n => n.id),
        }),
        headers: { "content-type": "application/json" },
      });

      const response = await bulkOperations(request);

      // Should only operate on notifications user has access to
      expect([200, 403]).toContain(response.status);

      if (response.status === 200) {
        const data = await response.json();
        // Should report partial success or filter out unauthorized notifications
        expect(data.processed).toBeLessThanOrEqual(notifications.length);
      }
    });

    it("should prevent bulk operations on excessive number of notifications", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create many notifications
      const manyNotifications = Array.from({ length: 1000 }, (_, i) => ({
        title: `Bulk Test ${i}`,
        message: `Message ${i}`,
        type: "INFO" as any,
        priority: "NORMAL" as any,
        targetType: "ALL_ADMINS" as any,
        createdBy: "admin-123",
      }));

      await prisma.notification.createMany({ data: manyNotifications });

      const notificationIds = await prisma.notification.findMany({
        where: { title: { contains: "Bulk Test" } },
        select: { id: true },
      });

      // Try to perform bulk operation on too many notifications
      const request = new NextRequest("http://localhost:3000/api/admin/notifications/bulk", {
        method: "POST",
        body: JSON.stringify({
          action: "delete",
          notificationIds: notificationIds.map(n => n.id),
        }),
        headers: { "content-type": "application/json" },
      });

      const response = await bulkOperations(request);

      // Should enforce reasonable limits or process in batches
      expect([200, 400, 429]).toContain(response.status);
    });
  });

  describe("Rate Limiting and DoS Protection", () => {
    it("should handle rapid notification creation attempts", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const rapidRequests = 50;
      const startTime = Date.now();

      // Create many rapid requests
      const promises = Array.from({ length: rapidRequests }, (_, i) => {
        const notificationData = {
          title: `Rapid Test ${i}`,
          message: `Rapid message ${i}`,
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
        };

        const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
          method: "POST",
          body: JSON.stringify(notificationData),
          headers: { "content-type": "application/json" },
        });

        return POST(request);
      });

      const responses = await Promise.all(promises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should handle rapid requests (may implement rate limiting)
      const successfulRequests = responses.filter(r => r.status === 201).length;
      
      // Either all succeed (no rate limiting) or some are rate limited
      expect(successfulRequests).toBeGreaterThan(0);
      expect(successfulRequests).toBeLessThanOrEqual(rapidRequests);

      // Should complete in reasonable time
      expect(duration).toBeLessThan(10000); // 10 seconds
    });

    it("should enforce reasonable query limits", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Try to query with excessive limit
      const request = new NextRequest("http://localhost:3000/api/admin/notifications?limit=10000");
      const response = await GET(request);

      expect(response.status).toBe(200);

      const data = await response.json();
      
      // Should enforce maximum limit
      expect(data.notifications.length).toBeLessThanOrEqual(100); // Reasonable max limit
    });
  });

  describe("Data Exposure Prevention", () => {
    it("should not expose sensitive information in notifications", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create notification with potentially sensitive metadata
      const notification = await prisma.notification.create({
        data: {
          title: "Sensitive Notification",
          message: "Contains sensitive data",
          type: "WARNING",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          createdBy: "admin-123",
          metadata: {
            userPassword: "secret-password",
            creditCard: "4111-1111-1111-1111",
            apiKey: "sk-1234567890abcdef",
            internalNote: "This should not be exposed",
          },
        },
      });

      const request = new NextRequest(`http://localhost:3000/api/admin/notifications/${notification.id}`);
      const response = await getById(request, { params: { id: notification.id } });

      expect(response.status).toBe(200);

      const data = await response.json();
      const responseString = JSON.stringify(data);
      
      // Should not expose sensitive data
      expect(responseString).not.toContain("secret-password");
      expect(responseString).not.toContain("4111-1111-1111-1111");
      expect(responseString).not.toContain("sk-1234567890abcdef");
    });

    it("should filter notifications based on user role", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      // Create notifications with different role targeting
      await prisma.notification.createMany({
        data: [
          {
            title: "All Admins",
            message: "For all admins",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ALL_ADMINS",
            createdBy: "admin-123",
          },
          {
            title: "Admin Role Only",
            message: "For admin role only",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ROLE_ADMIN",
            createdBy: "admin-123",
          },
          {
            title: "Moderator Role",
            message: "For moderator role",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ROLE_MODERATOR",
            createdBy: "admin-123",
          },
        ],
      });

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");
      const response = await GET(request);

      expect(response.status).toBe(200);

      const data = await response.json();
      const titles = data.notifications.map((n: any) => n.title);
      
      // Moderator should see ALL_ADMINS and ROLE_MODERATOR but not ROLE_ADMIN
      expect(titles).toContain("All Admins");
      expect(titles).toContain("Moderator Role");
      expect(titles).not.toContain("Admin Role Only");
    });
  });

  describe("Notification Integrity", () => {
    it("should prevent tampering with notification read status", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create notification
      const notification = await prisma.notification.create({
        data: {
          title: "Integrity Test",
          message: "Test message",
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
          isRead: false,
          createdBy: "admin-123",
        },
      });

      // Try to tamper with other properties while marking as read
      const request = new NextRequest(`http://localhost:3000/api/admin/notifications/${notification.id}`, {
        method: "PUT",
        body: JSON.stringify({
          isRead: true,
          title: "Tampered Title", // Should not be allowed
          priority: "URGENT", // Should not be allowed
          createdBy: "hacker", // Should not be allowed
        }),
        headers: { "content-type": "application/json" },
      });

      const response = await PUT(request, { params: { id: notification.id } });

      expect(response.status).toBe(200);

      // Verify only read status was changed
      const updatedNotification = await prisma.notification.findUnique({
        where: { id: notification.id },
      });

      expect(updatedNotification?.isRead).toBe(true);
      expect(updatedNotification?.title).toBe("Integrity Test"); // Should not be changed
      expect(updatedNotification?.priority).toBe("NORMAL"); // Should not be changed
      expect(updatedNotification?.createdBy).toBe("admin-123"); // Should not be changed
    });
  });
});
