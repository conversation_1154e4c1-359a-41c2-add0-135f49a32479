/**
 * Audit Logs Security Tests
 * <PERSON><PERSON><PERSON> tra bảo mật cho hệ thống audit logs
 */

import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/audit-logs/route";
import { GET as getById, PUT, DELETE } from "@/app/api/admin/audit-logs/[id]/route";
import { logAdminAction } from "@/lib/audit-logger";
import { getServerSession } from "next-auth";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Audit Logs Security Tests", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      type: "admin",
      role: "ADMIN",
      name: "Test Admin",
    },
  };

  const mockModeratorSession = {
    user: {
      id: "moderator-123",
      type: "admin",
      role: "MODERATOR",
      name: "Test Moderator",
    },
  };

  const mockUserSession = {
    user: {
      id: "user-123",
      type: "user",
      role: "USER",
      name: "Regular User",
    },
  };

  beforeEach(async () => {
    // Clean up test data
    await prisma.auditLog.deleteMany();
    await prisma.adminUser.deleteMany();

    // Create test admin users
    await prisma.adminUser.createMany({
      data: [
        {
          id: "admin-123",
          email: "<EMAIL>",
          name: "Test Admin",
          password: "hashed-password",
          role: "ADMIN",
        },
        {
          id: "moderator-123",
          email: "<EMAIL>",
          name: "Test Moderator",
          password: "hashed-password",
          role: "MODERATOR",
        },
      ],
    });
  });

  afterAll(async () => {
    await prisma.auditLog.deleteMany();
    await prisma.adminUser.deleteMany();
    await prisma.$disconnect();
  });

  describe("Authentication and Authorization", () => {
    it("should deny access to unauthenticated users", async () => {
      mockGetServerSession.mockResolvedValueOnce(null);

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      expect(response.status).toBe(401);
    });

    it("should deny access to regular users", async () => {
      mockGetServerSession.mockResolvedValueOnce(mockUserSession);

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      expect(response.status).toBe(401);
    });

    it("should allow access to admin users", async () => {
      mockGetServerSession.mockResolvedValueOnce(mockAdminSession);

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      expect(response.status).toBe(200);
    });

    it("should allow access to moderator users", async () => {
      mockGetServerSession.mockResolvedValueOnce(mockModeratorSession);

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      expect(response.status).toBe(200);
    });

    it("should prevent session hijacking attempts", async () => {
      // Test with malformed session
      mockGetServerSession.mockResolvedValueOnce({
        user: {
          id: "admin-123",
          type: "admin",
          role: "ADMIN",
          // Missing required fields
        },
      } as any);

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      // Should handle gracefully
      expect([200, 401]).toContain(response.status);
    });
  });

  describe("Data Integrity and Immutability", () => {
    it("should prevent modification of existing audit logs", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create an audit log
      const auditLog = await prisma.auditLog.create({
        data: {
          action: "CREATE",
          resource: "Product",
          resourceId: "product-1",
          description: "Original description",
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Test Agent",
        },
      });

      // Attempt to modify the audit log
      const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs/${auditLog.id}`, {
        method: "PUT",
        body: JSON.stringify({
          description: "Modified description",
          action: "DELETE", // Attempting to change action
        }),
        headers: { "content-type": "application/json" },
      });

      const response = await PUT(request, { params: { id: auditLog.id } });

      // Should not allow modification (405 Method Not Allowed or similar)
      expect([405, 403]).toContain(response.status);

      // Verify original data is unchanged
      const unchangedLog = await prisma.auditLog.findUnique({
        where: { id: auditLog.id },
      });

      expect(unchangedLog?.description).toBe("Original description");
      expect(unchangedLog?.action).toBe("CREATE");
    });

    it("should prevent deletion of audit logs by non-super-admin", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      // Create an audit log
      const auditLog = await prisma.auditLog.create({
        data: {
          action: "CREATE",
          resource: "Product",
          resourceId: "product-1",
          description: "Test log",
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Test Agent",
        },
      });

      // Attempt to delete the audit log as moderator
      const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs/${auditLog.id}`, {
        method: "DELETE",
      });

      const response = await DELETE(request, { params: { id: auditLog.id } });

      // Should not allow deletion
      expect([403, 405]).toContain(response.status);

      // Verify log still exists
      const existingLog = await prisma.auditLog.findUnique({
        where: { id: auditLog.id },
      });
      expect(existingLog).toBeTruthy();
    });

    it("should ensure audit log creation is atomic", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Mock database error during creation
      const originalCreate = prisma.auditLog.create;
      let callCount = 0;
      
      prisma.auditLog.create = jest.fn().mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          throw new Error("Database connection lost");
        }
        return originalCreate.call(prisma.auditLog, arguments[0]);
      });

      // Attempt to log an action
      await logAdminAction({
        action: "CREATE",
        resource: "Product",
        resourceId: "product-1",
        description: "Test action",
        adminId: "admin-123",
        ipAddress: "***********",
        userAgent: "Test Agent",
      });

      // Should not create partial or corrupted logs
      const logs = await prisma.auditLog.findMany({
        where: { description: "Test action" },
      });

      expect(logs).toHaveLength(0); // Should not create log if operation fails

      // Restore original function
      prisma.auditLog.create = originalCreate;
    });
  });

  describe("Input Validation and Sanitization", () => {
    it("should validate and sanitize audit log creation input", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const maliciousData = {
        action: "<script>alert('xss')</script>",
        resource: "'; DROP TABLE audit_logs; --",
        resourceId: "product-1",
        description: "<img src=x onerror=alert('xss')>",
        oldValues: {
          malicious: "<script>document.cookie</script>",
        },
        newValues: {
          injection: "'; DELETE FROM users; --",
        },
      };

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs", {
        method: "POST",
        body: JSON.stringify(maliciousData),
        headers: { "content-type": "application/json" },
      });

      const response = await POST(request);

      if (response.status === 201) {
        const data = await response.json();
        
        // Verify malicious content is sanitized or rejected
        expect(data.auditLog.action).not.toContain("<script>");
        expect(data.auditLog.resource).not.toContain("DROP TABLE");
        expect(data.auditLog.description).not.toContain("<img");
      } else {
        // Should reject malicious input
        expect([400, 422]).toContain(response.status);
      }
    });

    it("should validate query parameters for SQL injection", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const maliciousQueries = [
        "?action=' OR '1'='1",
        "?resource=Product'; DROP TABLE audit_logs; --",
        "?search='; DELETE FROM users; --",
        "?adminId=' UNION SELECT * FROM admin_users --",
      ];

      for (const query of maliciousQueries) {
        const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs${query}`);
        const response = await GET(request);

        // Should either sanitize input or return error, but not execute malicious SQL
        expect([200, 400, 422]).toContain(response.status);

        if (response.status === 200) {
          const data = await response.json();
          // Should return safe results, not expose database structure
          expect(data).toHaveProperty("auditLogs");
          expect(Array.isArray(data.auditLogs)).toBe(true);
        }
      }
    });

    it("should prevent XSS in audit log data", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create audit log with potential XSS content
      const auditLog = await prisma.auditLog.create({
        data: {
          action: "UPDATE",
          resource: "Product",
          resourceId: "product-1",
          description: "Updated product <script>alert('xss')</script>",
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Test Agent",
          oldValues: {
            name: "<img src=x onerror=alert('xss')>",
          },
          newValues: {
            name: "Safe Product Name",
          },
        },
      });

      const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs/${auditLog.id}`);
      const response = await getById(request, { params: { id: auditLog.id } });

      expect(response.status).toBe(200);

      const data = await response.json();
      
      // Verify XSS content is properly escaped or sanitized
      expect(data.auditLog.description).not.toContain("<script>");
      expect(JSON.stringify(data.auditLog.oldValues)).not.toContain("<img");
    });
  });

  describe("Rate Limiting and DoS Protection", () => {
    it("should handle rapid audit log creation attempts", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      const rapidRequests = 100;
      const startTime = Date.now();

      // Create many rapid requests
      const promises = Array.from({ length: rapidRequests }, (_, i) => {
        return logAdminAction({
          action: "CREATE",
          resource: "Product",
          resourceId: `rapid-product-${i}`,
          description: `Rapid test ${i}`,
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Rapid Test Agent",
        });
      });

      await Promise.all(promises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all logs were created (system should handle rapid creation)
      const count = await prisma.auditLog.count({
        where: { description: { contains: "Rapid test" } },
      });

      expect(count).toBe(rapidRequests);

      // Should complete in reasonable time (not be blocked by rate limiting for legitimate admin actions)
      expect(duration).toBeLessThan(10000); // 10 seconds
    });

    it("should handle large query result sets safely", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create many audit logs
      const largeDataset = 5000;
      const batchSize = 1000;

      for (let i = 0; i < largeDataset; i += batchSize) {
        const batch = Array.from({ length: Math.min(batchSize, largeDataset - i) }, (_, j) => ({
          action: "CREATE",
          resource: "Product",
          resourceId: `dos-test-${i + j}`,
          description: `DoS test ${i + j}`,
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "DoS Test Agent",
        }));

        await prisma.auditLog.createMany({ data: batch });
      }

      // Attempt to query all data at once
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs?limit=10000");
      const response = await GET(request);

      expect(response.status).toBe(200);

      const data = await response.json();
      
      // Should enforce reasonable limits
      expect(data.auditLogs.length).toBeLessThanOrEqual(100); // Should enforce max limit
      expect(data.pagination).toBeDefined();
    });
  });

  describe("Data Exposure Prevention", () => {
    it("should not expose sensitive information in audit logs", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Create audit log with potentially sensitive data
      const auditLog = await prisma.auditLog.create({
        data: {
          action: "UPDATE",
          resource: "User",
          resourceId: "user-1",
          description: "Updated user password",
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Test Agent",
          oldValues: {
            password: "old-hashed-password",
            email: "<EMAIL>",
            creditCard: "4111-1111-1111-1111",
          },
          newValues: {
            password: "new-hashed-password",
            email: "<EMAIL>",
          },
        },
      });

      const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs/${auditLog.id}`);
      const response = await getById(request, { params: { id: auditLog.id } });

      expect(response.status).toBe(200);

      const data = await response.json();
      
      // Should not expose sensitive data in API response
      const responseString = JSON.stringify(data);
      expect(responseString).not.toContain("4111-1111-1111-1111");
      
      // Password fields should be masked or excluded
      if (data.auditLog.oldValues?.password) {
        expect(data.auditLog.oldValues.password).toMatch(/^\*+$|^\[REDACTED\]$/);
      }
    });

    it("should not expose other admins' sensitive actions to moderators", async () => {
      mockGetServerSession.mockResolvedValue(mockModeratorSession);

      // Create sensitive audit log by admin
      await prisma.auditLog.create({
        data: {
          action: "DELETE",
          resource: "AdminUser",
          resourceId: "admin-456",
          description: "Deleted admin user",
          adminId: "admin-123", // Different admin
          ipAddress: "***********",
          userAgent: "Admin Agent",
        },
      });

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      expect(response.status).toBe(200);

      const data = await response.json();
      
      // Moderators might have limited access to sensitive admin actions
      // This depends on business requirements
      const sensitiveActions = data.auditLogs.filter((log: any) => 
        log.resource === "AdminUser" && log.action === "DELETE"
      );
      
      // Could be filtered out or have limited details
      expect(sensitiveActions.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe("Audit Trail Completeness", () => {
    it("should ensure all admin actions are logged", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Simulate various admin actions
      const actions = [
        { action: "CREATE", resource: "Product", resourceId: "product-1" },
        { action: "UPDATE", resource: "User", resourceId: "user-1" },
        { action: "DELETE", resource: "Category", resourceId: "category-1" },
        { action: "VIEW", resource: "Order", resourceId: "order-1" },
      ];

      for (const actionData of actions) {
        await logAdminAction({
          ...actionData,
          description: `${actionData.action} ${actionData.resource}`,
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Test Agent",
        });
      }

      // Verify all actions were logged
      const loggedActions = await prisma.auditLog.findMany({
        where: { adminId: "admin-123" },
        orderBy: { createdAt: "asc" },
      });

      expect(loggedActions).toHaveLength(actions.length);

      actions.forEach((action, index) => {
        expect(loggedActions[index].action).toBe(action.action);
        expect(loggedActions[index].resource).toBe(action.resource);
        expect(loggedActions[index].resourceId).toBe(action.resourceId);
      });
    });

    it("should maintain audit trail even during system errors", async () => {
      mockGetServerSession.mockResolvedValue(mockAdminSession);

      // Simulate system error during operation
      const originalConsoleError = console.error;
      console.error = jest.fn(); // Suppress error logs during test

      try {
        await logAdminAction({
          action: "CREATE",
          resource: "Product",
          resourceId: "error-product",
          description: "Action during error",
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Error Test Agent",
        });

        // Even if there are errors, audit log should be created
        const auditLog = await prisma.auditLog.findFirst({
          where: { resourceId: "error-product" },
        });

        expect(auditLog).toBeTruthy();
        expect(auditLog?.description).toBe("Action during error");
      } finally {
        console.error = originalConsoleError;
      }
    });
  });
});
