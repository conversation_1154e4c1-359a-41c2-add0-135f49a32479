/**
 * Audit Middleware Integration Tests
 * <PERSON><PERSON><PERSON> tra tích hợp cho audit middleware trong real API calls
 */

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { withAudit } from "@/lib/audit-middleware";
import { getServerSession } from "next-auth";
import { createMockNextRequest } from "../../helpers/test-utils";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<
  typeof getServerSession
>;

describe("Audit Middleware Integration Tests", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      type: "admin",
      role: "ADMIN",
      name: "Test Admin",
    },
  };

  beforeAll(async () => {
    // Set up authenticated session
    mockGetServerSession.mockResolvedValue(mockAdminSession);
  });

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock successful database operations
    (prisma.auditLog.create as jest.Mock).mockResolvedValue({
      id: "audit-123",
      action: "CREATE",
      resource: "Product",
      resourceId: "product-1",
      adminUserId: "admin-123",
      ipAddress: "***********",
      userAgent: "Test Browser",
      createdAt: new Date(),
    });

    (prisma.product.findUnique as jest.Mock).mockResolvedValue({
      id: "product-1",
      name: "Original Product",
      description: "Original description",
      price: 50000,
      stock: 25,
    });

    (prisma.product.create as jest.Mock).mockResolvedValue({
      id: "product-1",
      name: "Test Product",
      slug: "test-product",
      description: "Test product description",
      price: 100000,
      categoryId: "category-1",
      stock: 50,
    });
  });

  afterAll(async () => {
    // Clean up mocks
    jest.restoreAllMocks();
  });

  describe("Product Operations with Audit Logging", () => {
    // Mock product creation handler
    const createProductHandler = async (request: NextRequest) => {
      const body = await request.json();

      const product = await prisma.product.create({
        data: {
          name: body.name,
          slug: body.slug,
          description: body.description,
          price: body.price,
          categoryId: body.categoryId,
          stock: body.stock || 0,
          isActive: body.isActive ?? true,
        },
      });

      return NextResponse.json({ product }, { status: 201 });
    };

    // Mock product update handler
    const updateProductHandler = async (request: NextRequest, context: any) => {
      const body = await request.json();
      const { id } = context.params;

      const product = await prisma.product.update({
        where: { id },
        data: {
          name: body.name,
          description: body.description,
          price: body.price,
          stock: body.stock,
        },
      });

      return NextResponse.json({ product });
    };

    // Mock product deletion handler
    const deleteProductHandler = async (request: NextRequest, context: any) => {
      const { id } = context.params;

      await prisma.product.delete({
        where: { id },
      });

      return NextResponse.json({ message: "Product deleted" });
    };

    it("should log product creation with audit middleware", async () => {
      const auditedHandler = withAudit("CREATE", "Product", {
        getResourceId: (request, params, body) => body?.id,
        getNewValues: (request, params, body) => body,
        getDescription: (request, params, body) =>
          `Created product: ${body?.name}`,
      })(createProductHandler);

      const productData = {
        name: "Test Product",
        slug: "test-product",
        description: "Test product description",
        price: 100000,
        categoryId: "category-1",
        stock: 50,
      };

      const request = createMockNextRequest(
        "http://localhost:3000/api/admin/products",
        {
          method: "POST",
          body: JSON.stringify(productData),
          headers: {
            "content-type": "application/json",
            "x-forwarded-for": "***********",
            "user-agent": "Mozilla/5.0",
          },
        }
      );

      const response = await auditedHandler(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.product.name).toBe("Test Product");

      // Verify audit log was created
      const auditLog = await prisma.auditLog.findFirst({
        where: {
          action: "CREATE",
          resource: "Product",
          adminId: "admin-123",
        },
      });

      expect(auditLog).toBeTruthy();
      expect(auditLog?.description).toBe("Created product: Test Product");
      expect(auditLog?.newValues).toEqual(productData);
      expect(auditLog?.ipAddress).toBe("***********");
      expect(auditLog?.userAgent).toBe("Mozilla/5.0");
    });

    it("should log product update with old and new values", async () => {
      // Use mocked product data
      const product = {
        id: "product-1",
        name: "Original Product",
        slug: "original-product",
        description: "Original description",
        price: 50000,
        categoryId: "category-1",
        stock: 25,
      };

      const auditedHandler = withAudit("UPDATE", "Product", {
        getResourceId: (request, params) => params?.id,
        getOldValues: async (request, params) => {
          const product = await prisma.product.findUnique({
            where: { id: params?.id },
          });
          return product
            ? {
                name: product.name,
                description: product.description,
                price: product.price,
                stock: product.stock,
              }
            : undefined;
        },
        getNewValues: (request, params, body) => body,
        getDescription: (request, params, body) =>
          `Updated product: ${body?.name}`,
      })(updateProductHandler);

      const updateData = {
        name: "Updated Product",
        description: "Updated description",
        price: 75000,
        stock: 30,
      };

      const request = createMockNextRequest(
        `http://localhost:3000/api/admin/products/${product.id}`,
        {
          method: "PUT",
          body: JSON.stringify(updateData),
          headers: {
            "content-type": "application/json",
            "x-forwarded-for": "***********",
          },
        }
      );

      const response = await auditedHandler(request, {
        params: { id: product.id },
      });

      expect(response.status).toBe(200);

      // Verify audit log was created with old and new values
      const auditLog = await prisma.auditLog.findFirst({
        where: {
          action: "UPDATE",
          resource: "Product",
          resourceId: product.id,
          adminId: "admin-123",
        },
      });

      expect(auditLog).toBeTruthy();
      expect(auditLog?.oldValues).toEqual({
        name: "Original Product",
        description: "Original description",
        price: 50000,
        stock: 25,
      });
      expect(auditLog?.newValues).toEqual(updateData);
      expect(auditLog?.description).toBe("Updated product: Updated Product");
    });

    it("should log product deletion", async () => {
      // Use mocked product data
      const product = {
        id: "product-to-delete",
        name: "Product to Delete",
        slug: "product-to-delete",
        description: "This product will be deleted",
        price: 25000,
        categoryId: "category-1",
        stock: 10,
      };

      const auditedHandler = withAudit("DELETE", "Product", {
        getResourceId: (request, params) => params?.id,
        getOldValues: async (request, params) => {
          const product = await prisma.product.findUnique({
            where: { id: params?.id },
          });
          return product
            ? {
                name: product.name,
                description: product.description,
                price: product.price,
              }
            : undefined;
        },
        getDescription: (request, params) =>
          `Deleted product with ID: ${params?.id}`,
      })(deleteProductHandler);

      const request = createMockNextRequest(
        `http://localhost:3000/api/admin/products/${product.id}`,
        {
          method: "DELETE",
          headers: {
            "x-forwarded-for": "***********",
          },
        }
      );

      const response = await auditedHandler(request, {
        params: { id: product.id },
      });

      expect(response.status).toBe(200);

      // Verify product was deleted
      const deletedProduct = await prisma.product.findUnique({
        where: { id: product.id },
      });
      expect(deletedProduct).toBeNull();

      // Verify audit log was created
      const auditLog = await prisma.auditLog.findFirst({
        where: {
          action: "DELETE",
          resource: "Product",
          resourceId: product.id,
          adminId: "admin-123",
        },
      });

      expect(auditLog).toBeTruthy();
      expect(auditLog?.oldValues).toEqual({
        name: "Product to Delete",
        description: "This product will be deleted",
        price: 25000,
      });
      expect(auditLog?.description).toBe(
        `Deleted product with ID: ${product.id}`
      );
    });

    it("should not log audit when operation fails", async () => {
      const failingHandler = async (_request: NextRequest) => {
        throw new Error("Operation failed");
      };

      const auditedHandler = withAudit("CREATE", "Product")(failingHandler);

      const request = createMockNextRequest(
        "http://localhost:3000/api/admin/products",
        {
          method: "POST",
          body: JSON.stringify({ name: "Test Product" }),
          headers: { "content-type": "application/json" },
        }
      );

      await expect(auditedHandler(request)).rejects.toThrow("Operation failed");

      // Verify no audit log was created
      const auditLog = await prisma.auditLog.findFirst({
        where: {
          action: "CREATE",
          resource: "Product",
          adminId: "admin-123",
        },
      });

      expect(auditLog).toBeNull();
    });

    it("should handle concurrent operations with audit logging", async () => {
      const auditedHandler = withAudit("CREATE", "Product", {
        getNewValues: (request, params, body) => body,
        getDescription: (request, params, body) =>
          `Created product: ${body?.name}`,
      })(createProductHandler);

      const requests = Array.from({ length: 5 }, (_, i) => {
        const productData = {
          name: `Concurrent Product ${i}`,
          slug: `concurrent-product-${i}`,
          description: `Concurrent product ${i}`,
          price: 10000 * (i + 1),
          categoryId: "category-1",
          stock: 10,
        };

        return createMockNextRequest(
          "http://localhost:3000/api/admin/products",
          {
            method: "POST",
            body: JSON.stringify(productData),
            headers: {
              "content-type": "application/json",
              "x-forwarded-for": `192.168.1.${i + 1}`,
            },
          }
        );
      });

      const responses = await Promise.all(
        requests.map((req) => auditedHandler(req))
      );

      // All requests should succeed
      responses.forEach((response) => {
        expect(response.status).toBe(201);
      });

      // Verify all audit logs were created
      const auditLogs = await prisma.auditLog.findMany({
        where: {
          action: "CREATE",
          resource: "Product",
          adminId: "admin-123",
        },
      });

      expect(auditLogs).toHaveLength(5);

      // Verify each log has correct data
      auditLogs.forEach((log, index) => {
        expect(log.description).toBe(
          `Created product: Concurrent Product ${index}`
        );
        expect(log.ipAddress).toBe(`192.168.1.${index + 1}`);
      });
    });

    it("should handle large data in audit logs", async () => {
      const largeProductData = {
        name: "Large Product",
        slug: "large-product",
        description: "A".repeat(5000), // Large description
        price: 100000,
        categoryId: "category-1",
        stock: 100,
        metadata: {
          tags: Array.from({ length: 100 }, (_, i) => `tag-${i}`),
          attributes: Array.from({ length: 50 }, (_, i) => ({
            key: `attribute-${i}`,
            value: `value-${i}`.repeat(100),
          })),
        },
      };

      const auditedHandler = withAudit("CREATE", "Product", {
        getNewValues: (request, params, body) => body,
        getDescription: () => "Created large product",
      })(createProductHandler);

      const request = createMockNextRequest(
        "http://localhost:3000/api/admin/products",
        {
          method: "POST",
          body: JSON.stringify(largeProductData),
          headers: { "content-type": "application/json" },
        }
      );

      const response = await auditedHandler(request);

      expect(response.status).toBe(201);

      // Verify audit log was created with large data
      const auditLog = await prisma.auditLog.findFirst({
        where: {
          action: "CREATE",
          resource: "Product",
          adminId: "admin-123",
        },
      });

      expect(auditLog).toBeTruthy();
      expect(auditLog?.newValues).toEqual(largeProductData);
      expect(auditLog?.description).toBe("Created large product");
    });
  });

  describe("Error Handling", () => {
    it("should handle audit logging errors gracefully", async () => {
      // Mock prisma to fail audit log creation
      const originalCreate = prisma.auditLog.create;
      prisma.auditLog.create = jest
        .fn()
        .mockRejectedValue(new Error("Database error"));

      const successfulHandler = async (_request: NextRequest) => {
        return NextResponse.json({ success: true });
      };

      const auditedHandler = withAudit("CREATE", "Test")(successfulHandler);

      const request = createMockNextRequest("http://localhost:3000/api/test", {
        method: "POST",
      });

      // Should not throw error even if audit logging fails
      const response = await auditedHandler(request);

      expect(response.status).toBe(200);

      // Restore original function
      prisma.auditLog.create = originalCreate;
    });

    it("should handle session retrieval errors", async () => {
      mockGetServerSession.mockRejectedValueOnce(new Error("Session error"));

      const successfulHandler = async (_request: NextRequest) => {
        return NextResponse.json({ success: true });
      };

      const auditedHandler = withAudit("CREATE", "Test")(successfulHandler);

      const request = createMockNextRequest("http://localhost:3000/api/test", {
        method: "POST",
      });

      // Should not throw error even if session retrieval fails
      const response = await auditedHandler(request);

      expect(response.status).toBe(200);

      // Reset mock
      mockGetServerSession.mockResolvedValue(mockAdminSession);
    });
  });
});
