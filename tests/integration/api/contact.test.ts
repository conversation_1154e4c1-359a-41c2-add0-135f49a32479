import { NextRequest } from 'next/server';
import { POST } from '@/app/api/contact/route';
import { emailService } from '@/lib/email-service';

// Mock the email service
jest.mock('@/lib/email-service', () => ({
  emailService: {
    initialize: jest.fn(),
    sendContactFormEmail: jest.fn(),
  },
}));

const mockEmailService = emailService as jest.Mocked<typeof emailService>;

describe('/api/contact', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockEmailService.initialize.mockResolvedValue(true);
    mockEmailService.sendContactFormEmail.mockResolvedValue(true);
  });

  const validContactData = {
    name: '<PERSON>uyễn <PERSON>ăn A',
    email: '<EMAIL>',
    phone: '0123456789',
    subject: 'Hỏi về sản phẩm',
    message: 'Tô<PERSON> muốn hỏi về sản phẩm áo thun có sẵn không?',
  };

  it('should send contact form email successfully', async () => {
    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: JSON.stringify(validContactData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.message).toContain('Gửi tin nhắn thành công');
    expect(mockEmailService.initialize).toHaveBeenCalled();
    expect(mockEmailService.sendContactFormEmail).toHaveBeenCalledWith(
      expect.objectContaining({
        senderName: 'Nguyễn Văn A',
        senderEmail: '<EMAIL>',
        senderPhone: '0123456789',
        subject: 'Hỏi về sản phẩm',
        message: 'Tôi muốn hỏi về sản phẩm áo thun có sẵn không?',
      })
    );
  });

  it('should validate required fields', async () => {
    const invalidData = {
      name: 'A', // Too short
      email: 'invalid-email',
      subject: 'Hi', // Too short
      message: 'Short', // Too short
    };

    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: JSON.stringify(invalidData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBeDefined();
  });

  it('should handle missing required fields', async () => {
    const incompleteData = {
      name: 'Test User',
      // Missing email, subject, message
    };

    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: JSON.stringify(incompleteData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBeDefined();
  });

  it('should handle email service initialization failure', async () => {
    mockEmailService.initialize.mockResolvedValue(false);

    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: JSON.stringify(validContactData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(503);
    expect(data.error).toContain('Dịch vụ email chưa được cấu hình');
  });

  it('should handle email sending failure', async () => {
    mockEmailService.sendContactFormEmail.mockResolvedValue(false);

    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: JSON.stringify(validContactData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toContain('Không thể gửi email');
  });

  it('should handle optional phone field', async () => {
    const dataWithoutPhone = {
      ...validContactData,
    };
    delete dataWithoutPhone.phone;

    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: JSON.stringify(dataWithoutPhone),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(mockEmailService.sendContactFormEmail).toHaveBeenCalledWith(
      expect.objectContaining({
        senderName: 'Nguyễn Văn A',
        senderEmail: '<EMAIL>',
        senderPhone: undefined,
        subject: 'Hỏi về sản phẩm',
        message: 'Tôi muốn hỏi về sản phẩm áo thun có sẵn không?',
      })
    );
  });

  it('should handle malformed JSON', async () => {
    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: 'invalid json',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBeDefined();
  });

  it('should include timestamp in email data', async () => {
    const beforeTime = new Date().toISOString();
    
    const request = new NextRequest('http://localhost:3000/api/contact', {
      method: 'POST',
      body: JSON.stringify(validContactData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await POST(request);
    
    const afterTime = new Date().toISOString();

    expect(mockEmailService.sendContactFormEmail).toHaveBeenCalledWith(
      expect.objectContaining({
        submittedAt: expect.any(String),
      })
    );

    const callArgs = mockEmailService.sendContactFormEmail.mock.calls[0][0];
    const submittedAt = callArgs.submittedAt;
    
    expect(submittedAt >= beforeTime).toBe(true);
    expect(submittedAt <= afterTime).toBe(true);
  });
});
