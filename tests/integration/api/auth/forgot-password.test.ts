import { NextRequest } from 'next/server';
import { POST } from '@/app/api/auth/forgot-password/route';
import { prisma } from '@/lib/prisma';
import { emailService } from '@/lib/email-service';

// Mock the email service
jest.mock('@/lib/email-service', () => ({
  emailService: {
    initialize: jest.fn(),
    sendPasswordResetEmail: jest.fn(),
  },
}));

const mockEmailService = emailService as jest.Mocked<typeof emailService>;

describe('/api/auth/forgot-password', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockEmailService.initialize.mockResolvedValue(true);
    mockEmailService.sendPasswordResetEmail.mockResolvedValue(true);
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.passwordResetToken.deleteMany({
      where: {
        user: {
          email: {
            contains: 'test',
          },
        },
      },
    });
    await prisma.user.deleteMany({
      where: {
        email: {
          contains: 'test',
        },
      },
    });
  });

  it('should send password reset email for existing user', async () => {
    // Create test user
    const user = await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    });

    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.message).toContain('Nếu email tồn tại');

    // Check that reset token was created
    const resetToken = await prisma.passwordResetToken.findUnique({
      where: { userId: user.id },
    });
    expect(resetToken).toBeTruthy();
    expect(resetToken?.used).toBe(false);
    expect(resetToken?.expiresAt).toBeInstanceOf(Date);

    // Check that email was sent
    expect(mockEmailService.initialize).toHaveBeenCalled();
    expect(mockEmailService.sendPasswordResetEmail).toHaveBeenCalledWith(
      expect.objectContaining({
        recipientName: 'Test User',
        recipientEmail: '<EMAIL>',
        resetUrl: expect.stringContaining('reset-password?token='),
      })
    );
  });

  it('should return success even for non-existent user (security)', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.message).toContain('Nếu email tồn tại');

    // Should not send email for non-existent user
    expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
  });

  it('should validate email format', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: 'invalid-email' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toContain('Email không hợp lệ');
  });

  it('should require email field', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({}),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBeDefined();
  });

  it('should update existing reset token', async () => {
    // Create test user
    const user = await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    });

    // Create existing reset token
    const oldToken = await prisma.passwordResetToken.create({
      data: {
        userId: user.id,
        token: 'old-token',
        expiresAt: new Date(Date.now() + 15 * 60 * 1000),
        used: false,
      },
    });

    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await POST(request);

    // Check that token was updated
    const updatedToken = await prisma.passwordResetToken.findUnique({
      where: { userId: user.id },
    });
    expect(updatedToken?.token).not.toBe('old-token');
    expect(updatedToken?.used).toBe(false);
  });

  it('should handle email service initialization failure gracefully', async () => {
    mockEmailService.initialize.mockResolvedValue(false);

    // Create test user
    await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    });

    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    // Should still return success to user for security
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
  });

  it('should handle email sending failure gracefully', async () => {
    mockEmailService.sendPasswordResetEmail.mockRejectedValue(new Error('Email failed'));

    // Create test user
    await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    });

    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const response = await POST(request);
    const data = await response.json();

    // Should still return success to user for security
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
  });

  it('should generate unique tokens', async () => {
    // Create test users
    const user1 = await prisma.user.create({
      data: {
        name: 'Test User 1',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    });

    const user2 = await prisma.user.create({
      data: {
        name: 'Test User 2',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    });

    // Request reset for both users
    const request1 = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const request2 = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await POST(request1);
    await POST(request2);

    // Check that tokens are different
    const token1 = await prisma.passwordResetToken.findUnique({
      where: { userId: user1.id },
    });
    const token2 = await prisma.passwordResetToken.findUnique({
      where: { userId: user2.id },
    });

    expect(token1?.token).toBeTruthy();
    expect(token2?.token).toBeTruthy();
    expect(token1?.token).not.toBe(token2?.token);
  });

  it('should set correct expiry time (15 minutes)', async () => {
    // Create test user
    const user = await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
      },
    });

    const beforeTime = new Date();
    
    const request = new NextRequest('http://localhost:3000/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>' }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await POST(request);
    
    const afterTime = new Date();

    const resetToken = await prisma.passwordResetToken.findUnique({
      where: { userId: user.id },
    });

    expect(resetToken?.expiresAt).toBeTruthy();
    
    const expectedMinExpiry = new Date(beforeTime.getTime() + 14 * 60 * 1000); // 14 minutes
    const expectedMaxExpiry = new Date(afterTime.getTime() + 16 * 60 * 1000); // 16 minutes
    
    expect(resetToken!.expiresAt >= expectedMinExpiry).toBe(true);
    expect(resetToken!.expiresAt <= expectedMaxExpiry).toBe(true);
  });
});
