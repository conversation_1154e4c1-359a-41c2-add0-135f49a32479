/**
 * Audit Logs API Integration Tests
 * <PERSON><PERSON><PERSON> tra tích hợp cho API audit logs
 */

import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/audit-logs/route";
import { GET as getById, PUT, DELETE } from "@/app/api/admin/audit-logs/[id]/route";
import { getServerSession } from "next-auth";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Audit Logs API Integration Tests", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      type: "admin",
      role: "ADMIN",
      name: "Test Admin",
    },
  };

  beforeAll(async () => {
    // Set up authenticated session
    mockGetServerSession.mockResolvedValue(mockAdminSession);
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.auditLog.deleteMany();
    await prisma.adminUser.deleteMany();

    // Create test admin user
    await prisma.adminUser.create({
      data: {
        id: "admin-123",
        email: "<EMAIL>",
        name: "Test Admin",
        password: "hashed-password",
        role: "ADMIN",
      },
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.auditLog.deleteMany();
    await prisma.adminUser.deleteMany();
    await prisma.$disconnect();
  });

  describe("GET /api/admin/audit-logs", () => {
    beforeEach(async () => {
      // Create test audit logs
      await prisma.auditLog.createMany({
        data: [
          {
            id: "audit-1",
            action: "CREATE",
            resource: "Product",
            resourceId: "product-1",
            description: "Created new product",
            adminId: "admin-123",
            ipAddress: "***********",
            userAgent: "Mozilla/5.0",
            createdAt: new Date("2024-01-01T10:00:00Z"),
          },
          {
            id: "audit-2",
            action: "UPDATE",
            resource: "Product",
            resourceId: "product-1",
            description: "Updated product",
            adminId: "admin-123",
            ipAddress: "***********",
            userAgent: "Mozilla/5.0",
            createdAt: new Date("2024-01-01T11:00:00Z"),
          },
          {
            id: "audit-3",
            action: "DELETE",
            resource: "User",
            resourceId: "user-1",
            description: "Deleted user",
            adminId: "admin-123",
            ipAddress: "***********",
            userAgent: "Mozilla/5.0",
            createdAt: new Date("2024-01-01T12:00:00Z"),
          },
        ],
      });
    });

    it("should return paginated audit logs", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs?page=1&limit=2");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs).toHaveLength(2);
      expect(data.pagination.total).toBe(3);
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.limit).toBe(2);
      expect(data.pagination.totalPages).toBe(2);
    });

    it("should filter audit logs by action", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs?action=UPDATE");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs).toHaveLength(1);
      expect(data.auditLogs[0].action).toBe("UPDATE");
    });

    it("should filter audit logs by resource", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs?resource=Product");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs).toHaveLength(2);
      expect(data.auditLogs.every((log: any) => log.resource === "Product")).toBe(true);
    });

    it("should filter audit logs by date range", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/admin/audit-logs?startDate=2024-01-01T10:30:00Z&endDate=2024-01-01T11:30:00Z"
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs).toHaveLength(1);
      expect(data.auditLogs[0].action).toBe("UPDATE");
    });

    it("should search audit logs by description", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs?search=product");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs).toHaveLength(2);
      expect(data.auditLogs.every((log: any) => 
        log.description.toLowerCase().includes("product")
      )).toBe(true);
    });

    it("should sort audit logs by different fields", async () => {
      const request = new NextRequest(
        "http://localhost:3000/api/admin/audit-logs?sortBy=action&sortOrder=asc"
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs[0].action).toBe("CREATE");
      expect(data.auditLogs[1].action).toBe("DELETE");
      expect(data.auditLogs[2].action).toBe("UPDATE");
    });

    it("should include admin information in response", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs[0].admin).toEqual({
        id: "admin-123",
        name: "Test Admin",
        email: "<EMAIL>",
      });
    });

    it("should return 401 for unauthenticated requests", async () => {
      mockGetServerSession.mockResolvedValueOnce(null);

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      expect(response.status).toBe(401);
    });

    it("should return 401 for non-admin users", async () => {
      mockGetServerSession.mockResolvedValueOnce({
        user: { id: "user-123", type: "user" },
      });

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs");
      const response = await GET(request);

      expect(response.status).toBe(401);
    });
  });

  describe("GET /api/admin/audit-logs/[id]", () => {
    let testAuditLogId: string;

    beforeEach(async () => {
      const auditLog = await prisma.auditLog.create({
        data: {
          action: "UPDATE",
          resource: "Product",
          resourceId: "product-1",
          oldValues: { name: "Old Product" },
          newValues: { name: "New Product" },
          description: "Updated product name",
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Mozilla/5.0",
        },
      });
      testAuditLogId = auditLog.id;
    });

    it("should return specific audit log with details", async () => {
      const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs/${testAuditLogId}`);

      const response = await getById(request, { params: { id: testAuditLogId } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLog.id).toBe(testAuditLogId);
      expect(data.auditLog.action).toBe("UPDATE");
      expect(data.auditLog.resource).toBe("Product");
      expect(data.auditLog.oldValues).toEqual({ name: "Old Product" });
      expect(data.auditLog.newValues).toEqual({ name: "New Product" });
      expect(data.auditLog.admin).toEqual({
        id: "admin-123",
        name: "Test Admin",
        email: "<EMAIL>",
      });
    });

    it("should return 404 for non-existent audit log", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs/non-existent");

      const response = await getById(request, { params: { id: "non-existent" } });

      expect(response.status).toBe(404);
    });

    it("should return 401 for unauthenticated requests", async () => {
      mockGetServerSession.mockResolvedValueOnce(null);

      const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs/${testAuditLogId}`);
      const response = await getById(request, { params: { id: testAuditLogId } });

      expect(response.status).toBe(401);
    });
  });

  describe("POST /api/admin/audit-logs", () => {
    it("should create new audit log", async () => {
      const auditData = {
        action: "CREATE",
        resource: "Category",
        resourceId: "category-1",
        description: "Created new category",
        newValues: { name: "New Category" },
      };

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs", {
        method: "POST",
        body: JSON.stringify(auditData),
        headers: {
          "content-type": "application/json",
          "x-forwarded-for": "***********",
          "user-agent": "Mozilla/5.0",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.auditLog.action).toBe("CREATE");
      expect(data.auditLog.resource).toBe("Category");
      expect(data.auditLog.adminId).toBe("admin-123");
      expect(data.auditLog.ipAddress).toBe("***********");

      // Verify in database
      const createdLog = await prisma.auditLog.findUnique({
        where: { id: data.auditLog.id },
      });
      expect(createdLog).toBeTruthy();
    });

    it("should validate required fields", async () => {
      const invalidData = {
        resource: "Category", // missing action
      };

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs", {
        method: "POST",
        body: JSON.stringify(invalidData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it("should return 401 for unauthenticated requests", async () => {
      mockGetServerSession.mockResolvedValueOnce(null);

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs", {
        method: "POST",
        body: JSON.stringify({ action: "CREATE", resource: "Test" }),
      });

      const response = await POST(request);

      expect(response.status).toBe(401);
    });
  });

  describe("Database Integration", () => {
    it("should handle concurrent audit log creation", async () => {
      const auditData = {
        action: "CREATE",
        resource: "Product",
        description: "Concurrent test",
      };

      const requests = Array.from({ length: 5 }, (_, i) =>
        new NextRequest("http://localhost:3000/api/admin/audit-logs", {
          method: "POST",
          body: JSON.stringify({ ...auditData, resourceId: `product-${i}` }),
          headers: { "content-type": "application/json" },
        })
      );

      const responses = await Promise.all(requests.map(req => POST(req)));

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201);
      });

      // Verify all logs were created
      const count = await prisma.auditLog.count({
        where: { description: "Concurrent test" },
      });
      expect(count).toBe(5);
    });

    it("should handle large audit log data", async () => {
      const largeData = {
        action: "UPDATE",
        resource: "Product",
        resourceId: "product-large",
        description: "Large data test",
        oldValues: {
          description: "A".repeat(10000), // Large string
          metadata: Array.from({ length: 1000 }, (_, i) => ({ key: `value-${i}` })),
        },
        newValues: {
          description: "B".repeat(10000),
          metadata: Array.from({ length: 1000 }, (_, i) => ({ key: `new-value-${i}` })),
        },
      };

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs", {
        method: "POST",
        body: JSON.stringify(largeData),
        headers: { "content-type": "application/json" },
      });

      const response = await POST(request);

      expect(response.status).toBe(201);

      // Verify data integrity
      const data = await response.json();
      const createdLog = await prisma.auditLog.findUnique({
        where: { id: data.auditLog.id },
      });

      expect(createdLog?.oldValues).toEqual(largeData.oldValues);
      expect(createdLog?.newValues).toEqual(largeData.newValues);
    });
  });
});
