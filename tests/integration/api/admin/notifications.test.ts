/**
 * Notifications API Integration Tests
 * <PERSON><PERSON><PERSON> tra tích hợp cho API notifications
 */

import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/notifications/route";
import { GET as getById, PUT, DELETE } from "@/app/api/admin/notifications/[id]/route";
import { getServerSession } from "next-auth";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Notifications API Integration Tests", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      type: "admin",
      role: "ADMIN",
      name: "Test Admin",
    },
  };

  const mockModeratorSession = {
    user: {
      id: "moderator-123",
      type: "admin",
      role: "MODERATOR",
      name: "Test Moderator",
    },
  };

  beforeAll(async () => {
    // Set up authenticated session
    mockGetServerSession.mockResolvedValue(mockAdminSession);
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.notification.deleteMany();
    await prisma.adminUser.deleteMany();

    // Create test admin users
    await prisma.adminUser.createMany({
      data: [
        {
          id: "admin-123",
          email: "<EMAIL>",
          name: "Test Admin",
          password: "hashed-password",
          role: "ADMIN",
        },
        {
          id: "moderator-123",
          email: "<EMAIL>",
          name: "Test Moderator",
          password: "hashed-password",
          role: "MODERATOR",
        },
      ],
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.notification.deleteMany();
    await prisma.adminUser.deleteMany();
    await prisma.$disconnect();
  });

  describe("GET /api/admin/notifications", () => {
    beforeEach(async () => {
      // Create test notifications
      await prisma.notification.createMany({
        data: [
          {
            id: "notif-1",
            title: "General Notification",
            message: "This is for all admins",
            type: "INFO",
            priority: "NORMAL",
            targetType: "ALL_ADMINS",
            isRead: false,
            createdBy: "admin-123",
            createdAt: new Date("2024-01-01T10:00:00Z"),
          },
          {
            id: "notif-2",
            title: "Admin Only Notification",
            message: "This is for specific admin",
            type: "WARNING",
            priority: "HIGH",
            targetType: "SPECIFIC_ADMIN",
            targetId: "admin-123",
            isRead: true,
            createdBy: "admin-123",
            createdAt: new Date("2024-01-01T11:00:00Z"),
          },
          {
            id: "notif-3",
            title: "Role Based Notification",
            message: "This is for admin role",
            type: "SUCCESS",
            priority: "NORMAL",
            targetType: "ROLE_ADMIN",
            isRead: false,
            createdBy: "admin-123",
            createdAt: new Date("2024-01-01T12:00:00Z"),
          },
          {
            id: "notif-4",
            title: "Expired Notification",
            message: "This notification has expired",
            type: "INFO",
            priority: "LOW",
            targetType: "ALL_ADMINS",
            isRead: false,
            expiresAt: new Date("2023-12-31T23:59:59Z"), // Expired
            createdBy: "admin-123",
          },
        ],
      });
    });

    it("should return notifications for admin user", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/notifications");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.notifications).toHaveLength(3); // Should exclude expired notification
      expect(data.notifications.some((n: any) => n.id === "notif-4")).toBe(false); // Expired should be excluded
    });

    it("should return notifications for moderator user", async () => {
      mockGetServerSession.mockResolvedValueOnce(mockModeratorSession);

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      // Should get ALL_ADMINS notifications but not ROLE_ADMIN or SPECIFIC_ADMIN for admin-123
      expect(data.notifications).toHaveLength(1);
      expect(data.notifications[0].id).toBe("notif-1");
    });

    it("should filter unread notifications only", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/notifications?unreadOnly=true");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.notifications).toHaveLength(2); // notif-1 and notif-3 are unread
      expect(data.notifications.every((n: any) => !n.isRead)).toBe(true);
    });

    it("should filter notifications by type", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/notifications?type=WARNING");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.notifications).toHaveLength(1);
      expect(data.notifications[0].type).toBe("WARNING");
    });

    it("should paginate notifications correctly", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/notifications?page=1&limit=2");

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.notifications).toHaveLength(2);
      expect(data.pagination.total).toBe(3);
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.limit).toBe(2);
    });

    it("should return 403 for non-admin users", async () => {
      mockGetServerSession.mockResolvedValueOnce({
        user: { id: "user-123", type: "user" },
      });

      const request = new NextRequest("http://localhost:3000/api/admin/notifications");
      const response = await GET(request);

      expect(response.status).toBe(403);
    });
  });

  describe("POST /api/admin/notifications", () => {
    it("should create new notification", async () => {
      const notificationData = {
        title: "Test Notification",
        message: "This is a test notification",
        type: "INFO",
        priority: "NORMAL",
        targetType: "ALL_ADMINS",
      };

      const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
        method: "POST",
        body: JSON.stringify(notificationData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.notification.title).toBe("Test Notification");
      expect(data.notification.createdBy).toBe("admin-123");

      // Verify in database
      const createdNotification = await prisma.notification.findUnique({
        where: { id: data.notification.id },
      });
      expect(createdNotification).toBeTruthy();
    });

    it("should create notification with expiration date", async () => {
      const expirationDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
      const notificationData = {
        title: "Expiring Notification",
        message: "This notification will expire",
        type: "WARNING",
        priority: "HIGH",
        targetType: "ALL_ADMINS",
        expiresAt: expirationDate.toISOString(),
      };

      const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
        method: "POST",
        body: JSON.stringify(notificationData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(new Date(data.notification.expiresAt)).toEqual(expirationDate);
    });

    it("should create notification with metadata", async () => {
      const notificationData = {
        title: "Notification with Metadata",
        message: "This notification has metadata",
        type: "SYSTEM",
        priority: "URGENT",
        targetType: "SPECIFIC_ADMIN",
        targetId: "admin-123",
        actionUrl: "/admin/products/123",
        metadata: {
          productId: "123",
          action: "stock_low",
          threshold: 10,
        },
      };

      const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
        method: "POST",
        body: JSON.stringify(notificationData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.notification.metadata).toEqual(notificationData.metadata);
      expect(data.notification.actionUrl).toBe("/admin/products/123");
    });

    it("should validate required fields", async () => {
      const invalidData = {
        message: "Missing title", // title is required
      };

      const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
        method: "POST",
        body: JSON.stringify(invalidData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it("should validate enum values", async () => {
      const invalidData = {
        title: "Test",
        message: "Test message",
        type: "INVALID_TYPE", // Invalid enum value
      };

      const request = new NextRequest("http://localhost:3000/api/admin/notifications", {
        method: "POST",
        body: JSON.stringify(invalidData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });
  });

  describe("PUT /api/admin/notifications/[id]", () => {
    let testNotificationId: string;

    beforeEach(async () => {
      const notification = await prisma.notification.create({
        data: {
          title: "Test Notification",
          message: "Test message",
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
          isRead: false,
          createdBy: "admin-123",
        },
      });
      testNotificationId = notification.id;
    });

    it("should mark notification as read", async () => {
      const updateData = { isRead: true };

      const request = new NextRequest(`http://localhost:3000/api/admin/notifications/${testNotificationId}`, {
        method: "PUT",
        body: JSON.stringify(updateData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await PUT(request, { params: { id: testNotificationId } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.notification.isRead).toBe(true);
      expect(data.notification.readAt).toBeTruthy();

      // Verify in database
      const updatedNotification = await prisma.notification.findUnique({
        where: { id: testNotificationId },
      });
      expect(updatedNotification?.isRead).toBe(true);
      expect(updatedNotification?.readAt).toBeTruthy();
    });

    it("should mark notification as unread", async () => {
      // First mark as read
      await prisma.notification.update({
        where: { id: testNotificationId },
        data: { isRead: true, readAt: new Date() },
      });

      const updateData = { isRead: false };

      const request = new NextRequest(`http://localhost:3000/api/admin/notifications/${testNotificationId}`, {
        method: "PUT",
        body: JSON.stringify(updateData),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await PUT(request, { params: { id: testNotificationId } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.notification.isRead).toBe(false);
      expect(data.notification.readAt).toBeNull();
    });

    it("should return 404 for non-existent notification", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/notifications/non-existent", {
        method: "PUT",
        body: JSON.stringify({ isRead: true }),
        headers: {
          "content-type": "application/json",
        },
      });

      const response = await PUT(request, { params: { id: "non-existent" } });

      expect(response.status).toBe(404);
    });
  });

  describe("DELETE /api/admin/notifications/[id]", () => {
    let testNotificationId: string;

    beforeEach(async () => {
      const notification = await prisma.notification.create({
        data: {
          title: "Test Notification",
          message: "Test message",
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
          isRead: false,
          createdBy: "admin-123",
        },
      });
      testNotificationId = notification.id;
    });

    it("should delete notification", async () => {
      const request = new NextRequest(`http://localhost:3000/api/admin/notifications/${testNotificationId}`, {
        method: "DELETE",
      });

      const response = await DELETE(request, { params: { id: testNotificationId } });

      expect(response.status).toBe(200);

      // Verify deletion in database
      const deletedNotification = await prisma.notification.findUnique({
        where: { id: testNotificationId },
      });
      expect(deletedNotification).toBeNull();
    });

    it("should return 404 for non-existent notification", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/notifications/non-existent", {
        method: "DELETE",
      });

      const response = await DELETE(request, { params: { id: "non-existent" } });

      expect(response.status).toBe(404);
    });
  });
});
