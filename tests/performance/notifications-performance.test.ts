/**
 * Notifications Performance Tests
 * <PERSON><PERSON><PERSON> tra hiệu suất cho hệ thống notifications
 */

import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/notifications/route";
import { POST as bulkOperations } from "@/app/api/admin/notifications/bulk/route";
import { NotificationRulesEngine } from "@/lib/notification-rules";
import { sendNotificationEmails } from "@/lib/email-service";
import { getServerSession } from "next-auth";

// Mock dependencies
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

jest.mock("@/lib/email-service", () => ({
  sendNotificationEmails: jest.fn(),
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<
  typeof getServerSession
>;
const mockSendNotificationEmails =
  sendNotificationEmails as jest.MockedFunction<typeof sendNotificationEmails>;

describe("Notifications Performance Tests", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      type: "admin",
      role: "ADMIN",
      name: "Test Admin",
    },
  };

  beforeAll(async () => {
    mockGetServerSession.mockResolvedValue(mockAdminSession);
    mockSendNotificationEmails.mockResolvedValue(true);
  });

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock database operations
    (prisma.adminUser.findMany as jest.Mock).mockResolvedValue([
      {
        id: "admin-123",
        email: "<EMAIL>",
        name: "Test Admin",
        role: "ADMIN",
      },
      {
        id: "admin-456",
        email: "<EMAIL>",
        name: "Test Admin 2",
        role: "ADMIN",
      },
    ]);

    (prisma.notification.findMany as jest.Mock).mockResolvedValue([]);
    (prisma.notification.create as jest.Mock).mockResolvedValue({
      id: "notif-123",
      title: "Test Notification",
      message: "Test message",
      type: "INFO",
      priority: "NORMAL",
      createdAt: new Date(),
    });
    (prisma.notification.updateMany as jest.Mock).mockResolvedValue({
      count: 1,
    });
    (prisma.notification.deleteMany as jest.Mock).mockResolvedValue({
      count: 1,
    });
  });

  afterAll(async () => {
    // Clean up mocks
    jest.restoreAllMocks();
  });

  describe("Large Dataset Performance", () => {
    it("should handle querying large number of notifications efficiently", async () => {
      const totalNotifications = 5000;
      const batchSize = 500;

      console.log(
        `Creating ${totalNotifications} notifications for performance testing...`
      );
      const startTime = Date.now();

      // Create notifications in batches
      for (let i = 0; i < totalNotifications; i += batchSize) {
        const batch = Array.from(
          { length: Math.min(batchSize, totalNotifications - i) },
          (_, j) => ({
            title: `Performance Test Notification ${i + j}`,
            message: `This is performance test notification number ${i + j}`,
            type: ["INFO", "SUCCESS", "WARNING", "ERROR", "SYSTEM"][
              (i + j) % 5
            ] as any,
            priority: ["LOW", "NORMAL", "HIGH", "URGENT"][(i + j) % 4] as any,
            targetType: ["ALL_ADMINS", "SPECIFIC_ADMIN", "ROLE_ADMIN"][
              (i + j) % 3
            ] as any,
            targetId: (i + j) % 3 === 1 ? "admin-123" : null,
            isRead: (i + j) % 3 === 0,
            createdBy: "admin-123",
          })
        );

        await prisma.notification.createMany({ data: batch });
      }

      const creationTime = Date.now() - startTime;
      console.log(
        `Created ${totalNotifications} notifications in ${creationTime}ms`
      );

      // Test query performance
      const queryStartTime = Date.now();

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications?page=1&limit=50"
      );
      const response = await GET(request);
      const data = await response.json();

      const queryTime = Date.now() - queryStartTime;
      console.log(`Queried notifications in ${queryTime}ms`);

      expect(response.status).toBe(200);
      expect(data.notifications).toHaveLength(50);

      // Performance assertion: Query should complete within 1.5 seconds
      expect(queryTime).toBeLessThan(1500);
    });

    it("should handle filtering large notification datasets efficiently", async () => {
      const totalNotifications = 3000;

      // Create diverse test data
      const notifications = Array.from(
        { length: totalNotifications },
        (_, i) => ({
          title: `Filter Test ${i}`,
          message: `Message ${i}`,
          type: ["INFO", "WARNING", "ERROR"][i % 3] as any,
          priority: ["LOW", "NORMAL", "HIGH", "URGENT"][i % 4] as any,
          targetType: "ALL_ADMINS" as any,
          isRead: i % 2 === 0,
          createdBy: "admin-123",
        })
      );

      await prisma.notification.createMany({ data: notifications });

      // Test filtering performance
      const filterStartTime = Date.now();

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications?type=WARNING&unreadOnly=true&page=1&limit=20"
      );
      const response = await GET(request);

      const filterTime = Date.now() - filterStartTime;
      console.log(`Filtered notifications in ${filterTime}ms`);

      expect(response.status).toBe(200);

      // Performance assertion: Filtering should complete within 800ms
      expect(filterTime).toBeLessThan(800);
    });
  });

  describe("Concurrent Operations Performance", () => {
    it("should handle concurrent notification creation", async () => {
      const concurrentRequests = 30;

      console.log(
        `Testing ${concurrentRequests} concurrent notification creations...`
      );

      const startTime = Date.now();

      // Create concurrent notification operations
      const promises = Array.from(
        { length: concurrentRequests },
        async (_, i) => {
          const notificationData = {
            title: `Concurrent Notification ${i}`,
            message: `This is concurrent test notification ${i}`,
            type: "INFO",
            priority: "NORMAL",
            targetType: "ALL_ADMINS",
          };

          const request = new NextRequest(
            "http://localhost:3000/api/admin/notifications",
            {
              method: "POST",
              body: JSON.stringify(notificationData),
              headers: { "content-type": "application/json" },
            }
          );

          return POST(request);
        }
      );

      const responses = await Promise.all(promises);

      const totalTime = Date.now() - startTime;
      console.log(
        `Completed ${concurrentRequests} concurrent creations in ${totalTime}ms`
      );

      // Verify all notifications were created successfully
      responses.forEach((response) => {
        expect(response.status).toBe(201);
      });

      // Verify count in database
      const count = await prisma.notification.count({
        where: { title: { contains: "Concurrent Notification" } },
      });
      expect(count).toBe(concurrentRequests);

      // Performance assertion: Should handle concurrent operations efficiently
      expect(totalTime).toBeLessThan(4000); // 4 seconds for 30 concurrent operations
    });

    it("should handle concurrent notification queries efficiently", async () => {
      // Create test data
      await prisma.notification.createMany({
        data: Array.from({ length: 1000 }, (_, i) => ({
          title: `Query Test ${i}`,
          message: `Message ${i}`,
          type: "INFO" as any,
          priority: "NORMAL" as any,
          targetType: "ALL_ADMINS" as any,
          isRead: false,
          createdBy: "admin-123",
        })),
      });

      const concurrentQueries = 15;
      console.log(`Testing ${concurrentQueries} concurrent queries...`);

      const startTime = Date.now();

      // Create concurrent query operations
      const promises = Array.from(
        { length: concurrentQueries },
        async (_, i) => {
          const request = new NextRequest(
            `http://localhost:3000/api/admin/notifications?page=${(i % 5) + 1}&limit=20`
          );
          return GET(request);
        }
      );

      const responses = await Promise.all(promises);

      const totalTime = Date.now() - startTime;
      console.log(
        `Completed ${concurrentQueries} concurrent queries in ${totalTime}ms`
      );

      // Verify all queries succeeded
      responses.forEach((response) => {
        expect(response.status).toBe(200);
      });

      // Performance assertion: Concurrent queries should complete efficiently
      expect(totalTime).toBeLessThan(2500); // 2.5 seconds for 15 concurrent queries
    });
  });

  describe("Bulk Operations Performance", () => {
    it("should handle bulk mark as read efficiently", async () => {
      const totalNotifications = 2000;

      // Create unread notifications
      const notifications = Array.from(
        { length: totalNotifications },
        (_, i) => ({
          title: `Bulk Test ${i}`,
          message: `Message ${i}`,
          type: "INFO" as any,
          priority: "NORMAL" as any,
          targetType: "ALL_ADMINS" as any,
          isRead: false,
          createdBy: "admin-123",
        })
      );

      await prisma.notification.createMany({ data: notifications });

      // Get notification IDs
      const notificationIds = await prisma.notification.findMany({
        where: { title: { contains: "Bulk Test" } },
        select: { id: true },
      });

      // Test bulk mark as read performance
      const bulkStartTime = Date.now();

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/bulk",
        {
          method: "POST",
          body: JSON.stringify({
            action: "markAsRead",
            notificationIds: notificationIds.map((n) => n.id),
          }),
          headers: { "content-type": "application/json" },
        }
      );

      const response = await bulkOperations(request);

      const bulkTime = Date.now() - bulkStartTime;
      console.log(
        `Bulk marked ${totalNotifications} notifications as read in ${bulkTime}ms`
      );

      expect(response.status).toBe(200);

      // Verify all notifications were marked as read
      const readCount = await prisma.notification.count({
        where: { title: { contains: "Bulk Test" }, isRead: true },
      });
      expect(readCount).toBe(totalNotifications);

      // Performance assertion: Bulk operation should complete within 3 seconds
      expect(bulkTime).toBeLessThan(3000);
    });

    it("should handle bulk delete efficiently", async () => {
      const totalNotifications = 1500;

      // Create notifications for deletion
      const notifications = Array.from(
        { length: totalNotifications },
        (_, i) => ({
          title: `Delete Test ${i}`,
          message: `Message ${i}`,
          type: "INFO" as any,
          priority: "NORMAL" as any,
          targetType: "ALL_ADMINS" as any,
          isRead: true,
          createdBy: "admin-123",
        })
      );

      await prisma.notification.createMany({ data: notifications });

      // Get notification IDs
      const notificationIds = await prisma.notification.findMany({
        where: { title: { contains: "Delete Test" } },
        select: { id: true },
      });

      // Test bulk delete performance
      const deleteStartTime = Date.now();

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications/bulk",
        {
          method: "POST",
          body: JSON.stringify({
            action: "delete",
            notificationIds: notificationIds.map((n) => n.id),
          }),
          headers: { "content-type": "application/json" },
        }
      );

      const response = await bulkOperations(request);

      const deleteTime = Date.now() - deleteStartTime;
      console.log(
        `Bulk deleted ${totalNotifications} notifications in ${deleteTime}ms`
      );

      expect(response.status).toBe(200);

      // Verify all notifications were deleted
      const remainingCount = await prisma.notification.count({
        where: { title: { contains: "Delete Test" } },
      });
      expect(remainingCount).toBe(0);

      // Performance assertion: Bulk delete should complete within 2.5 seconds
      expect(deleteTime).toBeLessThan(2500);
    });
  });

  describe("Notification Rules Engine Performance", () => {
    it("should process events efficiently with many rules", async () => {
      const rulesEngine = NotificationRulesEngine.getInstance();

      // Add many rules
      const ruleCount = 100;
      for (let i = 0; i < ruleCount; i++) {
        await rulesEngine.addRule({
          id: `rule-${i}`,
          name: `Performance Rule ${i}`,
          description: `Performance test rule ${i}`,
          eventType: "product.stock_low",
          conditions: {
            stock: { operator: "<=", value: i + 10 },
          },
          notificationTemplate: {
            title: `Stock Alert ${i}`,
            message: `Product stock is low: {{stock}}`,
            type: "WARNING",
            priority: "HIGH",
            targetType: "ALL_ADMINS",
          },
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      // Test event processing performance
      const eventStartTime = Date.now();

      await rulesEngine.processEvent({
        type: "product.stock_low",
        data: {
          productId: "product-123",
          productName: "Test Product",
          stock: 5,
        },
        timestamp: new Date(),
      });

      const eventTime = Date.now() - eventStartTime;
      console.log(
        `Processed event against ${ruleCount} rules in ${eventTime}ms`
      );

      // Performance assertion: Event processing should complete quickly
      expect(eventTime).toBeLessThan(1000);
    });
  });

  describe("Email Notification Performance", () => {
    it("should handle bulk email sending efficiently", async () => {
      const emailCount = 50;

      // Create notifications for email sending
      const notifications = Array.from({ length: emailCount }, (_, i) => ({
        title: `Email Test ${i}`,
        message: `Email message ${i}`,
        type: "INFO" as any,
        priority: "NORMAL" as any,
        targetType: "ALL_ADMINS" as any,
        isRead: false,
        createdBy: "admin-123",
      }));

      const createdNotifications = await Promise.all(
        notifications.map((n) => prisma.notification.create({ data: n }))
      );

      // Test bulk email sending performance (mocked)
      const emailStartTime = Date.now();

      const emailPromises = createdNotifications.map((notification) =>
        sendNotificationEmails(notification.id)
      );

      await Promise.all(emailPromises);

      const emailTime = Date.now() - emailStartTime;
      console.log(
        `Processed ${emailCount} email notifications in ${emailTime}ms`
      );

      // Verify all emails were "sent" (mocked)
      expect(mockSendNotificationEmails).toHaveBeenCalledTimes(emailCount);

      // Performance assertion: Email processing should be efficient
      expect(emailTime).toBeLessThan(2000);
    });
  });

  describe("Memory Usage Performance", () => {
    it("should handle large notification queries without excessive memory usage", async () => {
      const totalNotifications = 4000;

      // Create large dataset with metadata
      const notifications = Array.from(
        { length: totalNotifications },
        (_, i) => ({
          title: `Memory Test ${i}`,
          message: `Memory test message ${i}`,
          type: "INFO" as any,
          priority: "NORMAL" as any,
          targetType: "ALL_ADMINS" as any,
          isRead: false,
          createdBy: "admin-123",
          metadata: {
            largeData: "X".repeat(500), // 500 bytes per notification
            index: i,
            timestamp: Date.now(),
          },
        })
      );

      await prisma.notification.createMany({ data: notifications });

      // Test memory usage during large query
      const initialMemory = process.memoryUsage();

      const request = new NextRequest(
        "http://localhost:3000/api/admin/notifications?page=1&limit=100"
      );
      const response = await GET(request);

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      console.log(
        `Memory increase during large query: ${Math.round(memoryIncrease / 1024 / 1024)}MB`
      );

      expect(response.status).toBe(200);

      // Performance assertion: Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe("Database Index Performance", () => {
    it("should utilize database indexes for notification queries", async () => {
      const totalNotifications = 1500;

      // Create test data with various patterns
      const notifications = Array.from(
        { length: totalNotifications },
        (_, i) => ({
          title: `Index Test ${i}`,
          message: `Message ${i}`,
          type: ["INFO", "WARNING", "ERROR", "SUCCESS"][i % 4] as any,
          priority: ["LOW", "NORMAL", "HIGH", "URGENT"][i % 4] as any,
          targetType: ["ALL_ADMINS", "SPECIFIC_ADMIN", "ROLE_ADMIN"][
            i % 3
          ] as any,
          targetId: i % 3 === 1 ? "admin-123" : null,
          isRead: i % 2 === 0,
          createdBy: i % 2 === 0 ? "admin-123" : "admin-456",
          createdAt: new Date(Date.now() - i * 1000), // 1 second intervals
        })
      );

      await prisma.notification.createMany({ data: notifications });

      // Test queries that should use indexes
      const testQueries = [
        "?type=WARNING",
        "?priority=HIGH",
        "?targetType=ALL_ADMINS",
        "?unreadOnly=true",
        "?type=ERROR&priority=URGENT",
      ];

      for (const query of testQueries) {
        const startTime = Date.now();

        const request = new NextRequest(
          `http://localhost:3000/api/admin/notifications${query}&page=1&limit=20`
        );
        const response = await GET(request);

        const queryTime = Date.now() - startTime;

        console.log(`Query "${query}" completed in ${queryTime}ms`);

        expect(response.status).toBe(200);
        // Each indexed query should complete quickly
        expect(queryTime).toBeLessThan(400);
      }
    });
  });
});
