/**
 * Audit Logs Performance Tests
 * <PERSON><PERSON><PERSON> tra hiệu su<PERSON>t cho hệ thống audit logs
 */

import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET } from "@/app/api/admin/audit-logs/route";
import { POST as exportAuditLogs } from "@/app/api/admin/audit-logs/export/route";
import { logAdminAction } from "@/lib/audit-logger";
import { getServerSession } from "next-auth";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Audit Logs Performance Tests", () => {
  const mockAdminSession = {
    user: {
      id: "admin-123",
      type: "admin",
      role: "ADMIN",
      name: "Test Admin",
    },
  };

  beforeAll(async () => {
    mockGetServerSession.mockResolvedValue(mockAdminSession);
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.auditLog.deleteMany();
    await prisma.adminUser.deleteMany();

    // Create test admin user
    await prisma.adminUser.create({
      data: {
        id: "admin-123",
        email: "<EMAIL>",
        name: "Test Admin",
        password: "hashed-password",
        role: "ADMIN",
      },
    });
  });

  afterAll(async () => {
    await prisma.auditLog.deleteMany();
    await prisma.adminUser.deleteMany();
    await prisma.$disconnect();
  });

  describe("Large Dataset Performance", () => {
    it("should handle querying large number of audit logs efficiently", async () => {
      // Create 10,000 audit logs
      const batchSize = 1000;
      const totalLogs = 10000;
      
      console.log(`Creating ${totalLogs} audit logs for performance testing...`);
      const startTime = Date.now();

      for (let i = 0; i < totalLogs; i += batchSize) {
        const batch = Array.from({ length: Math.min(batchSize, totalLogs - i) }, (_, j) => ({
          action: ["CREATE", "UPDATE", "DELETE"][j % 3],
          resource: ["Product", "User", "Category", "Order"][j % 4],
          resourceId: `resource-${i + j}`,
          description: `Performance test log ${i + j}`,
          adminId: "admin-123",
          ipAddress: `192.168.1.${(i + j) % 255}`,
          userAgent: "Performance Test Agent",
        }));

        await prisma.auditLog.createMany({ data: batch });
      }

      const creationTime = Date.now() - startTime;
      console.log(`Created ${totalLogs} audit logs in ${creationTime}ms`);

      // Test query performance
      const queryStartTime = Date.now();
      
      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs?page=1&limit=50");
      const response = await GET(request);
      const data = await response.json();

      const queryTime = Date.now() - queryStartTime;
      console.log(`Queried audit logs in ${queryTime}ms`);

      expect(response.status).toBe(200);
      expect(data.auditLogs).toHaveLength(50);
      expect(data.pagination.total).toBe(totalLogs);
      
      // Performance assertion: Query should complete within 2 seconds
      expect(queryTime).toBeLessThan(2000);
    });

    it("should handle filtering large datasets efficiently", async () => {
      // Create diverse test data
      const actions = ["CREATE", "UPDATE", "DELETE", "VIEW"];
      const resources = ["Product", "User", "Category", "Order", "Brand"];
      const totalLogs = 5000;

      console.log(`Creating ${totalLogs} diverse audit logs...`);
      
      const logs = Array.from({ length: totalLogs }, (_, i) => ({
        action: actions[i % actions.length],
        resource: resources[i % resources.length],
        resourceId: `resource-${i}`,
        description: `Test log ${i}`,
        adminId: "admin-123",
        ipAddress: `192.168.1.${i % 255}`,
        userAgent: "Test Agent",
      }));

      await prisma.auditLog.createMany({ data: logs });

      // Test filtering performance
      const filterStartTime = Date.now();

      const request = new NextRequest(
        "http://localhost:3000/api/admin/audit-logs?action=CREATE&resource=Product&page=1&limit=20"
      );
      const response = await GET(request);

      const filterTime = Date.now() - filterStartTime;
      console.log(`Filtered audit logs in ${filterTime}ms`);

      expect(response.status).toBe(200);
      
      // Performance assertion: Filtering should complete within 1 second
      expect(filterTime).toBeLessThan(1000);
    });

    it("should handle date range queries efficiently", async () => {
      // Create logs with different timestamps
      const totalLogs = 3000;
      const baseTime = Date.now();
      
      const logs = Array.from({ length: totalLogs }, (_, i) => ({
        action: "CREATE",
        resource: "Product",
        resourceId: `product-${i}`,
        description: `Date test log ${i}`,
        adminId: "admin-123",
        ipAddress: "***********",
        userAgent: "Test Agent",
        createdAt: new Date(baseTime - (i * 60000)), // 1 minute intervals
      }));

      await prisma.auditLog.createMany({ data: logs });

      // Test date range query performance
      const queryStartTime = Date.now();

      const startDate = new Date(baseTime - (1000 * 60000)).toISOString(); // 1000 minutes ago
      const endDate = new Date(baseTime - (500 * 60000)).toISOString(); // 500 minutes ago

      const request = new NextRequest(
        `http://localhost:3000/api/admin/audit-logs?startDate=${startDate}&endDate=${endDate}&page=1&limit=50`
      );
      const response = await GET(request);

      const queryTime = Date.now() - queryStartTime;
      console.log(`Date range query completed in ${queryTime}ms`);

      expect(response.status).toBe(200);
      
      // Performance assertion: Date range query should complete within 1.5 seconds
      expect(queryTime).toBeLessThan(1500);
    });
  });

  describe("Concurrent Operations Performance", () => {
    it("should handle concurrent audit log creation", async () => {
      const concurrentRequests = 50;
      const requestsPerBatch = 10;

      console.log(`Testing ${concurrentRequests} concurrent audit log creations...`);

      const startTime = Date.now();

      // Create concurrent audit log operations
      const promises = Array.from({ length: concurrentRequests }, async (_, i) => {
        return logAdminAction({
          action: "CREATE",
          resource: "Product",
          resourceId: `concurrent-product-${i}`,
          description: `Concurrent test ${i}`,
          adminId: "admin-123",
          ipAddress: `192.168.1.${i % 255}`,
          userAgent: "Concurrent Test Agent",
        });
      });

      await Promise.all(promises);

      const totalTime = Date.now() - startTime;
      console.log(`Completed ${concurrentRequests} concurrent operations in ${totalTime}ms`);

      // Verify all logs were created
      const count = await prisma.auditLog.count({
        where: { description: { contains: "Concurrent test" } },
      });

      expect(count).toBe(concurrentRequests);
      
      // Performance assertion: Should handle concurrent operations efficiently
      expect(totalTime).toBeLessThan(5000); // 5 seconds for 50 concurrent operations
    });

    it("should handle concurrent queries efficiently", async () => {
      // Create test data
      await prisma.auditLog.createMany({
        data: Array.from({ length: 1000 }, (_, i) => ({
          action: "CREATE",
          resource: "Product",
          resourceId: `product-${i}`,
          description: `Query test log ${i}`,
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Test Agent",
        })),
      });

      const concurrentQueries = 20;
      console.log(`Testing ${concurrentQueries} concurrent queries...`);

      const startTime = Date.now();

      // Create concurrent query operations
      const promises = Array.from({ length: concurrentQueries }, async (_, i) => {
        const request = new NextRequest(
          `http://localhost:3000/api/admin/audit-logs?page=${i % 5 + 1}&limit=20`
        );
        return GET(request);
      });

      const responses = await Promise.all(promises);

      const totalTime = Date.now() - startTime;
      console.log(`Completed ${concurrentQueries} concurrent queries in ${totalTime}ms`);

      // Verify all queries succeeded
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // Performance assertion: Concurrent queries should complete efficiently
      expect(totalTime).toBeLessThan(3000); // 3 seconds for 20 concurrent queries
    });
  });

  describe("Export Performance", () => {
    it("should handle large CSV export efficiently", async () => {
      const totalLogs = 5000;
      
      // Create test data for export
      console.log(`Creating ${totalLogs} logs for CSV export test...`);
      
      const logs = Array.from({ length: totalLogs }, (_, i) => ({
        action: ["CREATE", "UPDATE", "DELETE"][i % 3],
        resource: ["Product", "User", "Category"][i % 3],
        resourceId: `export-resource-${i}`,
        description: `Export test log ${i}`,
        adminId: "admin-123",
        ipAddress: "***********",
        userAgent: "Export Test Agent",
      }));

      await prisma.auditLog.createMany({ data: logs });

      // Test CSV export performance
      const exportStartTime = Date.now();

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs/export", {
        method: "POST",
        body: JSON.stringify({
          format: "csv",
          filters: {},
        }),
        headers: { "content-type": "application/json" },
      });

      const response = await exportAuditLogs(request);

      const exportTime = Date.now() - exportStartTime;
      console.log(`CSV export of ${totalLogs} logs completed in ${exportTime}ms`);

      expect(response.status).toBe(200);
      
      // Performance assertion: Export should complete within 10 seconds
      expect(exportTime).toBeLessThan(10000);
    });

    it("should handle large Excel export efficiently", async () => {
      const totalLogs = 3000;
      
      // Create test data for export
      const logs = Array.from({ length: totalLogs }, (_, i) => ({
        action: "UPDATE",
        resource: "Product",
        resourceId: `excel-product-${i}`,
        description: `Excel export test ${i}`,
        adminId: "admin-123",
        ipAddress: "***********",
        userAgent: "Excel Test Agent",
        oldValues: { name: `Old Product ${i}`, price: i * 100 },
        newValues: { name: `New Product ${i}`, price: i * 150 },
      }));

      await prisma.auditLog.createMany({ data: logs });

      // Test Excel export performance
      const exportStartTime = Date.now();

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs/export", {
        method: "POST",
        body: JSON.stringify({
          format: "excel",
          filters: { action: "UPDATE" },
        }),
        headers: { "content-type": "application/json" },
      });

      const response = await exportAuditLogs(request);

      const exportTime = Date.now() - exportStartTime;
      console.log(`Excel export of ${totalLogs} logs completed in ${exportTime}ms`);

      expect(response.status).toBe(200);
      
      // Performance assertion: Excel export should complete within 15 seconds
      expect(exportTime).toBeLessThan(15000);
    });
  });

  describe("Memory Usage Performance", () => {
    it("should handle large queries without excessive memory usage", async () => {
      const totalLogs = 8000;
      
      // Create large dataset
      const batchSize = 1000;
      for (let i = 0; i < totalLogs; i += batchSize) {
        const batch = Array.from({ length: Math.min(batchSize, totalLogs - i) }, (_, j) => ({
          action: "CREATE",
          resource: "Product",
          resourceId: `memory-test-${i + j}`,
          description: `Memory test log ${i + j}`,
          adminId: "admin-123",
          ipAddress: "***********",
          userAgent: "Memory Test Agent",
          oldValues: { data: "A".repeat(1000) }, // 1KB of data per log
          newValues: { data: "B".repeat(1000) },
        }));

        await prisma.auditLog.createMany({ data: batch });
      }

      // Test memory usage during large query
      const initialMemory = process.memoryUsage();

      const request = new NextRequest("http://localhost:3000/api/admin/audit-logs?page=1&limit=100");
      const response = await GET(request);

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      console.log(`Memory increase during large query: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);

      expect(response.status).toBe(200);
      
      // Performance assertion: Memory increase should be reasonable (less than 100MB)
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
    });
  });

  describe("Database Index Performance", () => {
    it("should utilize database indexes for common queries", async () => {
      const totalLogs = 2000;
      
      // Create test data with various patterns
      const logs = Array.from({ length: totalLogs }, (_, i) => ({
        action: ["CREATE", "UPDATE", "DELETE", "VIEW"][i % 4],
        resource: ["Product", "User", "Category", "Order", "Brand"][i % 5],
        resourceId: `index-test-${i}`,
        description: `Index test log ${i}`,
        adminId: i % 2 === 0 ? "admin-123" : "admin-456",
        ipAddress: "***********",
        userAgent: "Index Test Agent",
        createdAt: new Date(Date.now() - (i * 1000)), // 1 second intervals
      }));

      await prisma.auditLog.createMany({ data: logs });

      // Test queries that should use indexes
      const testQueries = [
        "?action=CREATE",
        "?resource=Product",
        "?adminId=admin-123",
        "?action=UPDATE&resource=User",
        `?startDate=${new Date(Date.now() - 1000000).toISOString()}`,
      ];

      for (const query of testQueries) {
        const startTime = Date.now();
        
        const request = new NextRequest(`http://localhost:3000/api/admin/audit-logs${query}&page=1&limit=20`);
        const response = await GET(request);
        
        const queryTime = Date.now() - startTime;
        
        console.log(`Query "${query}" completed in ${queryTime}ms`);
        
        expect(response.status).toBe(200);
        // Each indexed query should complete quickly
        expect(queryTime).toBeLessThan(500);
      }
    });
  });
});
