import { SendGridEmailService } from '@/lib/sendgrid-email-service';
import sgMail from '@sendgrid/mail';
import type {
  WelcomeEmailData,
  OrderConfirmationEmailData,
  ContactFormEmailData,
  PasswordResetEmailData,
  AdminNotificationEmailData,
} from '@/lib/email-templates';

// Mock SendGrid
jest.mock('@sendgrid/mail');
const mockSgMail = sgMail as jest.Mocked<typeof sgMail>;

describe('SendGridEmailService', () => {
  let emailService: SendGridEmailService;
  
  beforeEach(() => {
    emailService = new SendGridEmailService();
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env.SENDGRID_API_KEY = 'test-api-key';
    process.env.EMAIL_FROM = '<EMAIL>';
    process.env.EMAIL_FROM_NAME = 'NS Shop Test';
  });

  afterEach(() => {
    delete process.env.SENDGRID_API_KEY;
    delete process.env.EMAIL_FROM;
    delete process.env.EMAIL_FROM_NAME;
  });

  describe('initialize', () => {
    it('should initialize successfully with valid API key', async () => {
      const result = await emailService.initialize();
      
      expect(result).toBe(true);
      expect(mockSgMail.setApiKey).toHaveBeenCalledWith('test-api-key');
    });

    it('should fail to initialize without API key', async () => {
      delete process.env.SENDGRID_API_KEY;
      
      const result = await emailService.initialize();
      
      expect(result).toBe(false);
      expect(mockSgMail.setApiKey).not.toHaveBeenCalled();
    });
  });

  describe('sendWelcomeEmail', () => {
    const mockData: WelcomeEmailData = {
      recipientName: 'Test User',
      recipientEmail: '<EMAIL>',
      loginUrl: 'https://nsshop.com/auth/signin',
      supportEmail: '<EMAIL>',
    };

    beforeEach(async () => {
      await emailService.initialize();
    });

    it('should send welcome email successfully', async () => {
      mockSgMail.send.mockResolvedValue([{ statusCode: 202 } as any, {}]);
      
      const result = await emailService.sendWelcomeEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSgMail.send).toHaveBeenCalledWith(
        expect.objectContaining({
          to: {
            email: '<EMAIL>',
            name: 'Test User',
          },
          from: {
            email: '<EMAIL>',
            name: 'NS Shop Test',
          },
          subject: expect.stringContaining('Chào mừng Test User'),
        })
      );
    });

    it('should handle send failure', async () => {
      mockSgMail.send.mockRejectedValue(new Error('Send failed'));
      
      const result = await emailService.sendWelcomeEmail(mockData);
      
      expect(result).toBe(false);
    });
  });

  describe('sendOrderConfirmationEmail', () => {
    const mockData: OrderConfirmationEmailData = {
      recipientName: 'Test User',
      recipientEmail: '<EMAIL>',
      order: {
        id: 'order_123',
        total: 500000,
        status: 'PENDING',
        createdAt: '2024-01-15T10:30:00Z',
        items: [
          {
            name: 'Test Product',
            quantity: 1,
            price: 500000,
          },
        ],
        shippingAddress: {
          fullName: 'Test User',
          address: '123 Test St',
          city: 'Test City',
          postalCode: '12345',
          phone: '0123456789',
        },
      },
    };

    beforeEach(async () => {
      await emailService.initialize();
    });

    it('should send order confirmation email successfully', async () => {
      mockSgMail.send.mockResolvedValue([{ statusCode: 202 } as any, {}]);
      
      const result = await emailService.sendOrderConfirmationEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSgMail.send).toHaveBeenCalledWith(
        expect.objectContaining({
          to: {
            email: '<EMAIL>',
            name: 'Test User',
          },
          subject: expect.stringContaining('Xác nhận đơn hàng'),
        })
      );
    });
  });

  describe('sendContactFormEmail', () => {
    const mockData: ContactFormEmailData = {
      recipientName: 'Admin',
      recipientEmail: '<EMAIL>',
      senderName: 'Test User',
      senderEmail: '<EMAIL>',
      subject: 'Test Subject',
      message: 'Test message',
      submittedAt: '2024-01-15T10:30:00Z',
    };

    beforeEach(async () => {
      await emailService.initialize();
      process.env.ADMIN_EMAIL = '<EMAIL>';
    });

    it('should send contact form email to admin', async () => {
      mockSgMail.send.mockResolvedValue([{ statusCode: 202 } as any, {}]);
      
      const result = await emailService.sendContactFormEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSgMail.send).toHaveBeenCalledWith(
        expect.objectContaining({
          to: {
            email: '<EMAIL>',
            name: 'NS Shop Admin',
          },
          subject: expect.stringContaining('[Liên hệ] Test Subject'),
        })
      );
    });
  });

  describe('sendPasswordResetEmail', () => {
    const mockData: PasswordResetEmailData = {
      recipientName: 'Test User',
      recipientEmail: '<EMAIL>',
      resetUrl: 'https://nsshop.com/reset?token=abc123',
      expiresAt: '2024-01-15T10:45:00Z',
    };

    beforeEach(async () => {
      await emailService.initialize();
    });

    it('should send password reset email successfully', async () => {
      mockSgMail.send.mockResolvedValue([{ statusCode: 202 } as any, {}]);
      
      const result = await emailService.sendPasswordResetEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSgMail.send).toHaveBeenCalledWith(
        expect.objectContaining({
          to: {
            email: '<EMAIL>',
            name: 'Test User',
          },
          subject: 'Đặt lại mật khẩu tài khoản NS Shop',
        })
      );
    });
  });

  describe('sendAdminNotificationEmail', () => {
    const mockData: AdminNotificationEmailData = {
      recipientName: 'Admin User',
      recipientEmail: '<EMAIL>',
      notification: {
        id: 'notif_123',
        title: 'Test Notification',
        message: 'Test message',
        type: 'INFO',
        priority: 'NORMAL',
        createdAt: '2024-01-15T10:30:00Z',
      },
    };

    beforeEach(async () => {
      await emailService.initialize();
    });

    it('should send admin notification email successfully', async () => {
      mockSgMail.send.mockResolvedValue([{ statusCode: 202 } as any, {}]);
      
      const result = await emailService.sendAdminNotificationEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSgMail.send).toHaveBeenCalledWith(
        expect.objectContaining({
          to: {
            email: '<EMAIL>',
            name: 'Admin User',
          },
          subject: '[NS Shop Admin] Test Notification',
        })
      );
    });
  });

  describe('sendBulkAdminNotificationEmails', () => {
    const mockNotifications: AdminNotificationEmailData[] = [
      {
        recipientName: 'Admin 1',
        recipientEmail: '<EMAIL>',
        notification: {
          id: 'notif_1',
          title: 'Test 1',
          message: 'Message 1',
          type: 'INFO',
          priority: 'NORMAL',
          createdAt: '2024-01-15T10:30:00Z',
        },
      },
      {
        recipientName: 'Admin 2',
        recipientEmail: '<EMAIL>',
        notification: {
          id: 'notif_2',
          title: 'Test 2',
          message: 'Message 2',
          type: 'ERROR',
          priority: 'HIGH',
          createdAt: '2024-01-15T10:30:00Z',
        },
      },
    ];

    beforeEach(async () => {
      await emailService.initialize();
    });

    it('should send bulk emails successfully', async () => {
      mockSgMail.send.mockResolvedValue([{ statusCode: 202 } as any, {}]);
      
      const result = await emailService.sendBulkAdminNotificationEmails(mockNotifications);
      
      expect(result.sent).toBe(2);
      expect(result.failed).toBe(0);
      expect(result.errors).toHaveLength(0);
      expect(mockSgMail.send).toHaveBeenCalledTimes(2);
    });

    it('should handle partial failures', async () => {
      mockSgMail.send
        .mockResolvedValueOnce([{ statusCode: 202 } as any, {}])
        .mockRejectedValueOnce(new Error('Send failed'));
      
      const result = await emailService.sendBulkAdminNotificationEmails(mockNotifications);
      
      expect(result.sent).toBe(1);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('<EMAIL>');
    });
  });

  describe('testConnection', () => {
    it('should return true when initialized', async () => {
      await emailService.initialize();
      
      const result = await emailService.testConnection();
      
      expect(result).toBe(true);
    });

    it('should return false when not initialized', async () => {
      const result = await emailService.testConnection();
      
      expect(result).toBe(false);
    });
  });

  describe('sendTestEmail', () => {
    beforeEach(async () => {
      await emailService.initialize();
    });

    it('should send test email successfully', async () => {
      mockSgMail.send.mockResolvedValue([{ statusCode: 202 } as any, {}]);
      
      const result = await emailService.sendTestEmail('<EMAIL>', 'Test User');
      
      expect(result).toBe(true);
      expect(mockSgMail.send).toHaveBeenCalledWith(
        expect.objectContaining({
          to: {
            email: '<EMAIL>',
            name: 'Test User',
          },
          subject: '[NS Shop Admin] Test Email từ NS Shop',
        })
      );
    });
  });
});
