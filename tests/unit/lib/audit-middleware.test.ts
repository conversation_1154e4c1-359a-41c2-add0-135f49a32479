/**
 * Audit Middleware Unit Tests
 * Kiểm tra unit cho audit middleware functions
 */

import { withAudit, auditConfigs } from "@/lib/audit-middleware";
import { logAdminAction } from "@/lib/audit-logger";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

// Mock dependencies
jest.mock("@/lib/audit-logger", () => ({
  logAdminAction: jest.fn(),
}));

jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  adminAuthOptions: {},
}));

const mockLogAdminAction = logAdminAction as jest.MockedFunction<typeof logAdminAction>;
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Audit Middleware", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("withAudit", () => {
    const mockHandler = jest.fn();
    const mockAdminSession = {
      user: {
        id: "admin-123",
        type: "admin",
        role: "ADMIN",
      },
    };

    beforeEach(() => {
      mockHandler.mockResolvedValue(NextResponse.json({ success: true }));
      mockGetServerSession.mockResolvedValue(mockAdminSession);
    });

    it("should execute handler and log audit action for successful operations", async () => {
      const auditedHandler = withAudit("UPDATE", "Product", {
        getResourceId: () => "product-123",
        getDescription: () => "Updated product",
      })(mockHandler);

      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
        headers: {
          "x-forwarded-for": "***********",
          "user-agent": "Mozilla/5.0",
        },
      });

      const response = await auditedHandler(request, { params: { id: "123" } });

      expect(mockHandler).toHaveBeenCalledWith(request, { params: { id: "123" } });
      expect(mockLogAdminAction).toHaveBeenCalledWith({
        action: "UPDATE",
        resource: "Product",
        resourceId: "product-123",
        oldValues: undefined,
        newValues: undefined,
        description: "Updated product",
        adminId: "admin-123",
        ipAddress: "***********",
        userAgent: "Mozilla/5.0",
      });
      expect(response.status).toBe(200);
    });

    it("should not log audit action for non-admin users", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-123", type: "user" },
      });

      const auditedHandler = withAudit("UPDATE", "Product")(mockHandler);
      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
      });

      await auditedHandler(request);

      expect(mockHandler).toHaveBeenCalled();
      expect(mockLogAdminAction).not.toHaveBeenCalled();
    });

    it("should not log audit action for unauthenticated requests", async () => {
      mockGetServerSession.mockResolvedValue(null);

      const auditedHandler = withAudit("UPDATE", "Product")(mockHandler);
      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
      });

      await auditedHandler(request);

      expect(mockHandler).toHaveBeenCalled();
      expect(mockLogAdminAction).not.toHaveBeenCalled();
    });

    it("should not log audit action for failed operations", async () => {
      mockHandler.mockResolvedValue(NextResponse.json({ error: "Failed" }, { status: 400 }));

      const auditedHandler = withAudit("UPDATE", "Product")(mockHandler);
      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
      });

      const response = await auditedHandler(request);

      expect(mockHandler).toHaveBeenCalled();
      expect(mockLogAdminAction).not.toHaveBeenCalled();
      expect(response.status).toBe(400);
    });

    it("should skip logging when skipLogging condition is met", async () => {
      const auditedHandler = withAudit("UPDATE", "Product", {
        skipLogging: (request) => request.method === "GET",
      })(mockHandler);

      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "GET",
      });

      await auditedHandler(request);

      expect(mockHandler).toHaveBeenCalled();
      expect(mockLogAdminAction).not.toHaveBeenCalled();
    });

    it("should handle request body parsing for POST requests", async () => {
      const requestBody = { name: "New Product", price: 100 };
      const auditedHandler = withAudit("CREATE", "Product", {
        getNewValues: (request, params, body) => body,
      })(mockHandler);

      const request = new NextRequest("http://localhost:3000/api/admin/products", {
        method: "POST",
        body: JSON.stringify(requestBody),
        headers: {
          "content-type": "application/json",
        },
      });

      await auditedHandler(request);

      expect(mockLogAdminAction).toHaveBeenCalledWith(
        expect.objectContaining({
          newValues: requestBody,
        })
      );
    });

    it("should handle old values retrieval for UPDATE operations", async () => {
      const oldValues = { name: "Old Product", price: 50 };
      const auditedHandler = withAudit("UPDATE", "Product", {
        getOldValues: async () => oldValues,
        getResourceId: () => "product-123",
      })(mockHandler);

      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
      });

      await auditedHandler(request);

      expect(mockLogAdminAction).toHaveBeenCalledWith(
        expect.objectContaining({
          oldValues,
          resourceId: "product-123",
        })
      );
    });

    it("should handle errors in old values retrieval gracefully", async () => {
      const auditedHandler = withAudit("UPDATE", "Product", {
        getOldValues: async () => {
          throw new Error("Database error");
        },
      })(mockHandler);

      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
      });

      // Should not throw error
      await expect(auditedHandler(request)).resolves.not.toThrow();
      expect(mockHandler).toHaveBeenCalled();
    });

    it("should extract IP address from various headers", async () => {
      const testCases = [
        { header: "x-forwarded-for", value: "***********", expected: "***********" },
        { header: "x-real-ip", value: "********", expected: "********" },
      ];

      for (const testCase of testCases) {
        mockLogAdminAction.mockClear();

        const auditedHandler = withAudit("UPDATE", "Product")(mockHandler);
        const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
          method: "PUT",
          headers: {
            [testCase.header]: testCase.value,
          },
        });

        await auditedHandler(request);

        expect(mockLogAdminAction).toHaveBeenCalledWith(
          expect.objectContaining({
            ipAddress: testCase.expected,
          })
        );
      }
    });

    it("should handle non-JSON request bodies gracefully", async () => {
      const auditedHandler = withAudit("CREATE", "Product")(mockHandler);
      const request = new NextRequest("http://localhost:3000/api/admin/products", {
        method: "POST",
        body: "invalid-json",
        headers: {
          "content-type": "application/json",
        },
      });

      // Should not throw error
      await expect(auditedHandler(request)).resolves.not.toThrow();
      expect(mockHandler).toHaveBeenCalled();
    });
  });

  describe("auditConfigs", () => {
    it("should contain predefined configurations", () => {
      expect(auditConfigs).toHaveProperty("createProduct");
      expect(auditConfigs).toHaveProperty("updateProduct");
      expect(auditConfigs).toHaveProperty("deleteProduct");
      expect(auditConfigs).toHaveProperty("createUser");
      expect(auditConfigs).toHaveProperty("updateUser");
    });

    it("should have valid configuration structure", () => {
      Object.values(auditConfigs).forEach(config => {
        expect(config).toHaveProperty("action");
        expect(config).toHaveProperty("resource");
        expect(typeof config.action).toBe("string");
        expect(typeof config.resource).toBe("string");
      });
    });

    it("should have correct product configurations", () => {
      expect(auditConfigs.createProduct.action).toBe("CREATE");
      expect(auditConfigs.createProduct.resource).toBe("Product");
      
      expect(auditConfigs.updateProduct.action).toBe("UPDATE");
      expect(auditConfigs.updateProduct.resource).toBe("Product");
      
      expect(auditConfigs.deleteProduct.action).toBe("DELETE");
      expect(auditConfigs.deleteProduct.resource).toBe("Product");
    });

    it("should have resource ID extraction functions", () => {
      const mockRequest = new NextRequest("http://localhost:3000/api/admin/products/123");
      const mockParams = { id: "123" };

      if (auditConfigs.updateProduct.getResourceId) {
        const resourceId = auditConfigs.updateProduct.getResourceId(mockRequest, mockParams);
        expect(resourceId).toBe("123");
      }
    });
  });

  describe("Error Handling", () => {
    it("should handle audit logging errors gracefully", async () => {
      mockLogAdminAction.mockRejectedValue(new Error("Audit logging failed"));

      const auditedHandler = withAudit("UPDATE", "Product")(mockHandler);
      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
      });

      // Should not throw error and should still execute handler
      const response = await auditedHandler(request);

      expect(mockHandler).toHaveBeenCalled();
      expect(response.status).toBe(200);
    });

    it("should handle session retrieval errors", async () => {
      mockGetServerSession.mockRejectedValue(new Error("Session error"));

      const auditedHandler = withAudit("UPDATE", "Product")(mockHandler);
      const request = new NextRequest("http://localhost:3000/api/admin/products/123", {
        method: "PUT",
      });

      // Should not throw error
      await expect(auditedHandler(request)).resolves.not.toThrow();
      expect(mockHandler).toHaveBeenCalled();
    });
  });
});
