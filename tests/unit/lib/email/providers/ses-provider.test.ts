import { SESProvider } from '@/lib/email/providers/ses-provider';
import { SESClient, SendEmailCommand, GetSendQuotaCommand } from '@aws-sdk/client-ses';
import type {
  WelcomeEmailData,
  OrderConfirmationEmailData,
  ContactFormEmailData,
  PasswordResetEmailData,
  AdminNotificationEmailData,
} from '@/lib/email/templates';

// Mock AWS SES
jest.mock('@aws-sdk/client-ses');
const mockSESClient = SESClient as jest.MockedClass<typeof SESClient>;
const mockSend = jest.fn();

describe('SESProvider', () => {
  let sesProvider: SESProvider;
  
  beforeEach(() => {
    sesProvider = new SESProvider();
    jest.clearAllMocks();
    
    // Mock SES client
    mockSESClient.mockImplementation(() => ({
      send: mockSend,
    } as any));
    
    // Mock environment variables
    process.env.AWS_REGION = 'us-east-1';
    process.env.AWS_ACCESS_KEY_ID = 'test-access-key';
    process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key';
    process.env.SES_FROM_EMAIL = '<EMAIL>';
    process.env.SES_FROM_NAME = 'NS Shop Test';
  });

  afterEach(() => {
    delete process.env.AWS_REGION;
    delete process.env.AWS_ACCESS_KEY_ID;
    delete process.env.AWS_SECRET_ACCESS_KEY;
    delete process.env.SES_FROM_EMAIL;
    delete process.env.SES_FROM_NAME;
  });

  describe('initialize', () => {
    it('should initialize successfully with valid AWS credentials', async () => {
      const result = await sesProvider.initialize();
      
      expect(result).toBe(true);
      expect(mockSESClient).toHaveBeenCalledWith({
        region: 'us-east-1',
        credentials: {
          accessKeyId: 'test-access-key',
          secretAccessKey: 'test-secret-key',
        },
      });
    });

    it('should fail to initialize without AWS credentials', async () => {
      delete process.env.AWS_ACCESS_KEY_ID;
      delete process.env.AWS_SECRET_ACCESS_KEY;
      
      const result = await sesProvider.initialize();
      
      expect(result).toBe(false);
      expect(mockSESClient).not.toHaveBeenCalled();
    });

    it('should use default region if not specified', async () => {
      delete process.env.AWS_REGION;
      
      await sesProvider.initialize();
      
      expect(mockSESClient).toHaveBeenCalledWith({
        region: 'us-east-1',
        credentials: {
          accessKeyId: 'test-access-key',
          secretAccessKey: 'test-secret-key',
        },
      });
    });
  });

  describe('sendWelcomeEmail', () => {
    const mockData: WelcomeEmailData = {
      recipientName: 'Test User',
      recipientEmail: '<EMAIL>',
      loginUrl: 'https://nsshop.com/auth/signin',
      supportEmail: '<EMAIL>',
    };

    beforeEach(async () => {
      await sesProvider.initialize();
    });

    it('should send welcome email successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' });
      
      const result = await sesProvider.sendWelcomeEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.any(SendEmailCommand)
      );
    });

    it('should handle send failure', async () => {
      mockSend.mockRejectedValue(new Error('Send failed'));
      
      const result = await sesProvider.sendWelcomeEmail(mockData);
      
      expect(result).toBe(false);
    });
  });

  describe('sendOrderConfirmationEmail', () => {
    const mockData: OrderConfirmationEmailData = {
      recipientName: 'Test User',
      recipientEmail: '<EMAIL>',
      order: {
        id: 'order_123',
        total: 500000,
        status: 'PENDING',
        createdAt: '2024-01-15T10:30:00Z',
        items: [
          {
            name: 'Test Product',
            quantity: 1,
            price: 500000,
          },
        ],
        shippingAddress: {
          fullName: 'Test User',
          address: '123 Test St',
          city: 'Test City',
          postalCode: '12345',
          phone: '**********',
        },
      },
    };

    beforeEach(async () => {
      await sesProvider.initialize();
    });

    it('should send order confirmation email successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' });
      
      const result = await sesProvider.sendOrderConfirmationEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.any(SendEmailCommand)
      );
    });
  });

  describe('sendContactFormEmail', () => {
    const mockData: ContactFormEmailData = {
      recipientName: 'Admin',
      recipientEmail: '<EMAIL>',
      senderName: 'Test User',
      senderEmail: '<EMAIL>',
      subject: 'Test Subject',
      message: 'Test message',
      submittedAt: '2024-01-15T10:30:00Z',
    };

    beforeEach(async () => {
      await sesProvider.initialize();
      process.env.ADMIN_EMAIL = '<EMAIL>';
    });

    it('should send contact form email to admin', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' });
      
      const result = await sesProvider.sendContactFormEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.any(SendEmailCommand)
      );
    });
  });

  describe('sendPasswordResetEmail', () => {
    const mockData: PasswordResetEmailData = {
      recipientName: 'Test User',
      recipientEmail: '<EMAIL>',
      resetUrl: 'https://nsshop.com/reset?token=abc123',
      expiresAt: '2024-01-15T10:45:00Z',
    };

    beforeEach(async () => {
      await sesProvider.initialize();
    });

    it('should send password reset email successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' });
      
      const result = await sesProvider.sendPasswordResetEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.any(SendEmailCommand)
      );
    });
  });

  describe('sendAdminNotificationEmail', () => {
    const mockData: AdminNotificationEmailData = {
      recipientName: 'Admin User',
      recipientEmail: '<EMAIL>',
      notification: {
        id: 'notif_123',
        title: 'Test Notification',
        message: 'Test message',
        type: 'INFO',
        priority: 'NORMAL',
        createdAt: '2024-01-15T10:30:00Z',
      },
    };

    beforeEach(async () => {
      await sesProvider.initialize();
    });

    it('should send admin notification email successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' });
      
      const result = await sesProvider.sendAdminNotificationEmail(mockData);
      
      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.any(SendEmailCommand)
      );
    });
  });

  describe('sendBulkAdminNotificationEmails', () => {
    const mockNotifications: AdminNotificationEmailData[] = [
      {
        recipientName: 'Admin 1',
        recipientEmail: '<EMAIL>',
        notification: {
          id: 'notif_1',
          title: 'Test 1',
          message: 'Message 1',
          type: 'INFO',
          priority: 'NORMAL',
          createdAt: '2024-01-15T10:30:00Z',
        },
      },
      {
        recipientName: 'Admin 2',
        recipientEmail: '<EMAIL>',
        notification: {
          id: 'notif_2',
          title: 'Test 2',
          message: 'Message 2',
          type: 'ERROR',
          priority: 'HIGH',
          createdAt: '2024-01-15T10:30:00Z',
        },
      },
    ];

    beforeEach(async () => {
      await sesProvider.initialize();
    });

    it('should send bulk emails successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' });
      
      const result = await sesProvider.sendBulkAdminNotificationEmails(mockNotifications);
      
      expect(result.sent).toBe(2);
      expect(result.failed).toBe(0);
      expect(result.errors).toHaveLength(0);
      expect(mockSend).toHaveBeenCalledTimes(2);
    });

    it('should handle partial failures', async () => {
      mockSend
        .mockResolvedValueOnce({ MessageId: 'test-message-id' })
        .mockRejectedValueOnce(new Error('Send failed'));
      
      const result = await sesProvider.sendBulkAdminNotificationEmails(mockNotifications);
      
      expect(result.sent).toBe(1);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('<EMAIL>');
    });
  });

  describe('testConnection', () => {
    it('should return true when connection is successful', async () => {
      await sesProvider.initialize();
      mockSend.mockResolvedValue({ Max24HourSend: 200, MaxSendRate: 1 });
      
      const result = await sesProvider.testConnection();
      
      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.any(GetSendQuotaCommand)
      );
    });

    it('should return false when connection fails', async () => {
      await sesProvider.initialize();
      mockSend.mockRejectedValue(new Error('Connection failed'));
      
      const result = await sesProvider.testConnection();
      
      expect(result).toBe(false);
    });

    it('should return false when not initialized', async () => {
      const result = await sesProvider.testConnection();
      
      expect(result).toBe(false);
    });
  });

  describe('sendTestEmail', () => {
    beforeEach(async () => {
      await sesProvider.initialize();
    });

    it('should send test email successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' });
      
      const result = await sesProvider.sendTestEmail('<EMAIL>', 'Test User');
      
      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(
        expect.any(SendEmailCommand)
      );
    });
  });

  describe('getProviderName', () => {
    it('should return correct provider name', () => {
      const name = sesProvider.getProviderName();
      
      expect(name).toBe('Amazon SES');
    });
  });

  describe('getStatus', () => {
    it('should return correct status when configured and connected', async () => {
      await sesProvider.initialize();
      mockSend.mockResolvedValue({ Max24HourSend: 200, MaxSendRate: 1 });
      
      const status = await sesProvider.getStatus();
      
      expect(status.configured).toBe(true);
      expect(status.connected).toBe(true);
      expect(status.provider).toBe('Amazon SES');
      expect(status.message).toContain('connected and ready');
    });

    it('should return correct status when not configured', async () => {
      delete process.env.AWS_ACCESS_KEY_ID;
      delete process.env.AWS_SECRET_ACCESS_KEY;
      
      const status = await sesProvider.getStatus();
      
      expect(status.configured).toBe(false);
      expect(status.connected).toBe(false);
      expect(status.provider).toBe('Amazon SES');
      expect(status.message).toContain('not configured');
    });
  });
});
