import { EmailProviderFactory } from '@/lib/email/providers/factory';
import { SendGridProvider } from '@/lib/email/providers/sendgrid-provider';
import { SESProvider } from '@/lib/email/providers/ses-provider';

// Mock the providers
jest.mock('@/lib/email/providers/sendgrid-provider');
jest.mock('@/lib/email/providers/ses-provider');

const mockSendGridProvider = SendGridProvider as jest.MockedClass<typeof SendGridProvider>;
const mockSESProvider = SESProvider as jest.MockedClass<typeof SESProvider>;

describe('EmailProviderFactory', () => {
  let factory: EmailProviderFactory;
  
  beforeEach(() => {
    factory = EmailProviderFactory.getInstance();
    factory.clearCache();
    jest.clearAllMocks();
    
    // Mock provider instances
    mockSendGridProvider.mockImplementation(() => ({
      initialize: jest.fn().mockResolvedValue(true),
      getProviderName: jest.fn().mockReturnValue('SendGrid'),
      getStatus: jest.fn().mockResolvedValue({
        connected: true,
        configured: true,
        message: 'SendGrid is connected',
        provider: 'SendGrid',
      }),
      sendTestEmail: jest.fn().mockResolvedValue(true),
    } as any));

    mockSESProvider.mockImplementation(() => ({
      initialize: jest.fn().mockResolvedValue(true),
      getProviderName: jest.fn().mockReturnValue('Amazon SES'),
      getStatus: jest.fn().mockResolvedValue({
        connected: true,
        configured: true,
        message: 'Amazon SES is connected',
        provider: 'Amazon SES',
      }),
      sendTestEmail: jest.fn().mockResolvedValue(true),
    } as any));
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.EMAIL_PROVIDER;
    delete process.env.SENDGRID_API_KEY;
    delete process.env.AWS_ACCESS_KEY_ID;
    delete process.env.AWS_SECRET_ACCESS_KEY;
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = EmailProviderFactory.getInstance();
      const instance2 = EmailProviderFactory.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('createProvider', () => {
    it('should create SendGrid provider', () => {
      const provider = factory.createProvider('sendgrid');
      
      expect(provider).toBeInstanceOf(SendGridProvider);
      expect(mockSendGridProvider).toHaveBeenCalled();
    });

    it('should create SES provider', () => {
      const provider = factory.createProvider('ses');
      
      expect(provider).toBeInstanceOf(SESProvider);
      expect(mockSESProvider).toHaveBeenCalled();
    });

    it('should cache providers', () => {
      const provider1 = factory.createProvider('sendgrid');
      const provider2 = factory.createProvider('sendgrid');
      
      expect(provider1).toBe(provider2);
      expect(mockSendGridProvider).toHaveBeenCalledTimes(1);
    });

    it('should throw error for unsupported provider type', () => {
      expect(() => {
        factory.createProvider('unsupported' as any);
      }).toThrow('Unsupported email provider type: unsupported');
    });

    describe('auto provider selection', () => {
      it('should select SendGrid when API key is available', () => {
        process.env.SENDGRID_API_KEY = 'test-key';
        
        const provider = factory.createProvider('auto');
        
        expect(provider).toBeInstanceOf(SendGridProvider);
      });

      it('should select SES when AWS credentials are available', () => {
        process.env.AWS_ACCESS_KEY_ID = 'test-key';
        process.env.AWS_SECRET_ACCESS_KEY = 'test-secret';
        
        const provider = factory.createProvider('auto');
        
        expect(provider).toBeInstanceOf(SESProvider);
      });

      it('should prefer SendGrid over SES when both are configured', () => {
        process.env.SENDGRID_API_KEY = 'test-key';
        process.env.AWS_ACCESS_KEY_ID = 'test-key';
        process.env.AWS_SECRET_ACCESS_KEY = 'test-secret';
        
        const provider = factory.createProvider('auto');
        
        expect(provider).toBeInstanceOf(SendGridProvider);
      });

      it('should default to SendGrid when no configuration found', () => {
        const provider = factory.createProvider('auto');
        
        expect(provider).toBeInstanceOf(SendGridProvider);
      });
    });
  });

  describe('getProviderTypeFromEnv', () => {
    it('should return sendgrid for EMAIL_PROVIDER=sendgrid', () => {
      process.env.EMAIL_PROVIDER = 'sendgrid';
      
      const type = EmailProviderFactory.getProviderTypeFromEnv();
      
      expect(type).toBe('sendgrid');
    });

    it('should return ses for EMAIL_PROVIDER=ses', () => {
      process.env.EMAIL_PROVIDER = 'ses';
      
      const type = EmailProviderFactory.getProviderTypeFromEnv();
      
      expect(type).toBe('ses');
    });

    it('should return ses for EMAIL_PROVIDER=amazon-ses', () => {
      process.env.EMAIL_PROVIDER = 'amazon-ses';
      
      const type = EmailProviderFactory.getProviderTypeFromEnv();
      
      expect(type).toBe('ses');
    });

    it('should return ses for EMAIL_PROVIDER=aws-ses', () => {
      process.env.EMAIL_PROVIDER = 'aws-ses';
      
      const type = EmailProviderFactory.getProviderTypeFromEnv();
      
      expect(type).toBe('ses');
    });

    it('should return auto for EMAIL_PROVIDER=auto', () => {
      process.env.EMAIL_PROVIDER = 'auto';
      
      const type = EmailProviderFactory.getProviderTypeFromEnv();
      
      expect(type).toBe('auto');
    });

    it('should return auto for unknown EMAIL_PROVIDER', () => {
      process.env.EMAIL_PROVIDER = 'unknown';
      
      const type = EmailProviderFactory.getProviderTypeFromEnv();
      
      expect(type).toBe('auto');
    });

    it('should return auto when EMAIL_PROVIDER is not set', () => {
      const type = EmailProviderFactory.getProviderTypeFromEnv();
      
      expect(type).toBe('auto');
    });
  });

  describe('createFromEnv', () => {
    it('should create provider based on environment variable', () => {
      process.env.EMAIL_PROVIDER = 'sendgrid';
      
      const provider = EmailProviderFactory.createFromEnv();
      
      expect(provider).toBeInstanceOf(SendGridProvider);
    });

    it('should create auto provider when EMAIL_PROVIDER is not set', () => {
      const provider = EmailProviderFactory.createFromEnv();
      
      expect(provider).toBeInstanceOf(SendGridProvider); // Default auto behavior
    });
  });

  describe('getAllProvidersStatus', () => {
    it('should return status for all providers', async () => {
      const statuses = await factory.getAllProvidersStatus();
      
      expect(statuses).toHaveLength(2);
      expect(statuses[0].type).toBe('sendgrid');
      expect(statuses[0].name).toBe('SendGrid');
      expect(statuses[1].type).toBe('ses');
      expect(statuses[1].name).toBe('Amazon SES');
    });

    it('should handle provider initialization errors', async () => {
      mockSendGridProvider.mockImplementation(() => {
        throw new Error('Initialization failed');
      });
      
      const statuses = await factory.getAllProvidersStatus();
      
      expect(statuses[0].configured).toBe(false);
      expect(statuses[0].connected).toBe(false);
      expect(statuses[0].message).toContain('Error');
    });
  });

  describe('testAllProviders', () => {
    it('should test all providers successfully', async () => {
      const results = await factory.testAllProviders('<EMAIL>');
      
      expect(results).toHaveLength(2);
      expect(results[0].provider).toBe('SendGrid');
      expect(results[0].success).toBe(true);
      expect(results[1].provider).toBe('Amazon SES');
      expect(results[1].success).toBe(true);
    });

    it('should handle provider test failures', async () => {
      mockSendGridProvider.mockImplementation(() => ({
        initialize: jest.fn().mockResolvedValue(false),
        getProviderName: jest.fn().mockReturnValue('SendGrid'),
      } as any));
      
      const results = await factory.testAllProviders('<EMAIL>');
      
      expect(results[0].success).toBe(false);
      expect(results[0].error).toBe('Provider not initialized');
    });

    it('should handle provider creation errors', async () => {
      mockSESProvider.mockImplementation(() => {
        throw new Error('Creation failed');
      });
      
      const results = await factory.testAllProviders('<EMAIL>');
      
      expect(results[1].success).toBe(false);
      expect(results[1].error).toContain('Error');
    });
  });

  describe('clearCache', () => {
    it('should clear cached providers', () => {
      const provider1 = factory.createProvider('sendgrid');
      factory.clearCache();
      const provider2 = factory.createProvider('sendgrid');
      
      expect(provider1).not.toBe(provider2);
      expect(mockSendGridProvider).toHaveBeenCalledTimes(2);
    });
  });
});
