/**
 * Notification Rules Engine Unit Tests
 * Ki<PERSON>m tra unit cho notification rules engine
 */

import {
  NotificationRulesEngine,
  NotificationRule,
  NotificationEvent,
} from "@/lib/notification-rules";
import { prisma } from "@/lib/prisma";
import { sendNotificationEmails } from "@/lib/email-service";

// Mock dependencies
jest.mock("@/lib/prisma", () => ({
  prisma: {
    notification: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock("@/lib/email-service", () => ({
  sendNotificationEmails: jest.fn(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockSendNotificationEmails =
  sendNotificationEmails as jest.MockedFunction<typeof sendNotificationEmails>;

describe("NotificationRulesEngine", () => {
  let rulesEngine: NotificationRulesEngine;

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset singleton instance
    (NotificationRulesEngine as any).instance = undefined;
    rulesEngine = NotificationRulesEngine.getInstance();
    jest.spyOn(console, "log").mockImplementation(() => {});
    jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("Singleton Pattern", () => {
    it("should return the same instance", () => {
      const instance1 = NotificationRulesEngine.getInstance();
      const instance2 = NotificationRulesEngine.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe("Rule Management", () => {
    const mockRuleInput = {
      name: "Low Stock Alert",
      description: "Alert when product stock is low",
      eventType: "product.stock_low",
      conditions: {
        stockLevel: { operator: "<=", value: 10 },
      },
      notificationTemplate: {
        title: "Low Stock Alert",
        message: "Product {{productName}} has low stock: {{stockLevel}}",
        type: "WARNING" as const,
        priority: "HIGH" as const,
        targetType: "ALL_ADMINS" as const,
        actionUrl: "/admin/products/{{productId}}",
      },
      isActive: true,
    };

    describe("addRule", () => {
      it("should add a new rule", async () => {
        const addedRule = await rulesEngine.addRule(mockRuleInput);

        // Verify rule was added and has generated ID
        expect(addedRule.id).toMatch(/^custom-\d+$/);
        expect(addedRule.name).toBe(mockRuleInput.name);
        expect(addedRule.eventType).toBe(mockRuleInput.eventType);

        // Verify rule was added to internal map
        const rules = (rulesEngine as any).rules.get("product.stock_low");
        expect(rules).toHaveLength(1);
        expect(rules[0].name).toBe("Low Stock Alert");
      });

      it("should handle multiple rules for same event type", async () => {
        const rule2Input = { ...mockRuleInput, name: "Critical Stock Alert" };

        const rule1 = await rulesEngine.addRule(mockRuleInput);
        const rule2 = await rulesEngine.addRule(rule2Input);

        const rules = (rulesEngine as any).rules.get("product.stock_low");
        expect(rules).toHaveLength(2);
        expect(
          rules.find((r: NotificationRule) => r.name === "Low Stock Alert")
        ).toBeTruthy();
        expect(
          rules.find((r: NotificationRule) => r.name === "Critical Stock Alert")
        ).toBeTruthy();
      });
    });

    describe("getRulesForEvent", () => {
      it("should return rules for specific event type", async () => {
        await rulesEngine.addRule(mockRuleInput);

        const rules = rulesEngine.getRulesForEvent("product.stock_low");
        expect(rules).toHaveLength(1);
        expect(rules[0].name).toBe("Low Stock Alert");
      });

      it("should return empty array for non-existent event type", () => {
        const rules = rulesEngine.getRulesForEvent("non-existent-event");
        expect(rules).toEqual([]);
      });
    });

    describe("getAllRules", () => {
      it("should return all rules", async () => {
        await rulesEngine.addRule(mockRuleInput);
        await rulesEngine.addRule({
          ...mockRuleInput,
          eventType: "order.created",
          name: "Order Alert",
        });

        const allRules = rulesEngine.getAllRules();
        expect(allRules.length).toBeGreaterThanOrEqual(2);
        expect(allRules.some((r) => r.name === "Low Stock Alert")).toBe(true);
        expect(allRules.some((r) => r.name === "Order Alert")).toBe(true);
      });
    });
  });

  describe("Event Processing", () => {
    const mockEvent: NotificationEvent = {
      type: "product.stock_low",
      data: {
        productId: "product-123",
        productName: "Test Product",
        stockLevel: 5,
      },
      timestamp: new Date(),
    };

    beforeEach(async () => {
      const mockRuleInput = {
        name: "Low Stock Alert",
        description: "Alert when product stock is low",
        eventType: "product.stock_low",
        conditions: {
          stockLevel: { operator: "<=", value: 10 },
        },
        notificationTemplate: {
          title: "Low Stock Alert",
          message: "Product {{productName}} has low stock: {{stockLevel}}",
          type: "WARNING" as const,
          priority: "HIGH" as const,
          targetType: "ALL_ADMINS" as const,
        },
        isActive: true,
      };

      await rulesEngine.addRule(mockRuleInput);
    });

    describe("processEvent", () => {
      it("should process event and trigger matching rules", async () => {
        mockPrisma.notification.create.mockResolvedValue({
          id: "notification-123",
          title: "Low Stock Alert",
          message: "Product Test Product has low stock: 5",
          type: "WARNING",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          isRead: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as any);

        await rulesEngine.processEvent(mockEvent);

        expect(mockPrisma.notification.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            title: "Low Stock Alert",
            message: "Product Test Product has low stock: 5",
            type: "WARNING",
            priority: "HIGH",
            targetType: "ALL_ADMINS",
            metadata: expect.objectContaining({
              eventType: "product.stock_low",
              eventData: mockEvent.data,
            }),
            createdBy: "system",
          }),
        });
      });

      it("should not trigger inactive rules", async () => {
        // Add inactive rule
        const inactiveRuleInput = {
          name: "Inactive Rule",
          description: "This rule is inactive",
          eventType: "product.stock_low",
          conditions: {},
          notificationTemplate: {
            title: "Should not trigger",
            message: "This should not be created",
            type: "INFO" as const,
            priority: "NORMAL" as const,
            targetType: "ALL_ADMINS" as const,
          },
          isActive: false,
        };

        await rulesEngine.addRule(inactiveRuleInput);
        await rulesEngine.processEvent(mockEvent);

        // Should only create one notification (from active rule)
        expect(mockPrisma.notification.create).toHaveBeenCalledTimes(1);
      });

      it("should handle events with no matching rules", async () => {
        const unmatchedEvent: NotificationEvent = {
          type: "unknown.event",
          data: {},
          timestamp: new Date(),
        };

        await rulesEngine.processEvent(unmatchedEvent);

        expect(mockPrisma.notification.create).not.toHaveBeenCalled();
      });

      it("should handle rule processing errors gracefully", async () => {
        const consoleErrorSpy = jest
          .spyOn(console, "error")
          .mockImplementation();
        mockPrisma.notification.create.mockRejectedValue(
          new Error("Database error")
        );

        await rulesEngine.processEvent(mockEvent);

        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringContaining("Error processing rule"),
          expect.any(Error)
        );
      });
    });

    describe("Template Variable Replacement Integration", () => {
      it("should replace template variables in notification creation", async () => {
        mockPrisma.notification.create.mockResolvedValue({
          id: "notification-123",
          title: "Low Stock Alert",
          message: "Product Test Product has low stock: 5",
          type: "WARNING",
          priority: "HIGH",
          targetType: "ALL_ADMINS",
          isRead: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as any);

        await rulesEngine.processEvent(mockEvent);

        // Verify template variables were replaced correctly
        expect(mockPrisma.notification.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            title: "Low Stock Alert",
            message: "Product Test Product has low stock: 5",
          }),
        });
      });
    });
  });
});
