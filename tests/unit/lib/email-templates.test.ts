import {
  generateWelcomeEmail,
  generateOrderConfirmationEmail,
  generateContactFormEmail,
  generatePasswordResetEmail,
  generateAdminNotificationEmail,
  getBaseTemplate,
  type WelcomeEmailData,
  type OrderConfirmationEmailData,
  type ContactFormEmailData,
  type PasswordResetEmailData,
  type AdminNotificationEmailData,
} from "@/lib/email-templates";

describe("Email Templates", () => {
  describe("getBaseTemplate", () => {
    it("should generate base template with content and title", () => {
      const content = "<h1>Test Content</h1>";
      const title = "Test Title";

      const result = getBaseTemplate(content, title);

      expect(result).toContain("<!DOCTYPE html>");
      expect(result).toContain("<title>Test Title</title>");
      expect(result).toContain("<h1>Test Content</h1>");
      expect(result).toContain("NS Shop");
      expect(result).toContain("Th<PERSON><PERSON> trang trự<PERSON> tuyến chất lượng cao");
    });
  });

  describe("generateWelcomeEmail", () => {
    const mockData: WelcomeEmailData = {
      recipientName: "Nguyễn Văn A",
      recipientEmail: "<EMAIL>",
      loginUrl: "https://nsshop.com/auth/signin",
      supportEmail: "<EMAIL>",
    };

    it("should generate welcome email with correct subject", () => {
      const result = generateWelcomeEmail(mockData);

      expect(result.subject).toBe("Chào mừng Nguyễn Văn A đến với NS Shop! 🎉");
    });

    it("should include recipient name in HTML content", () => {
      const result = generateWelcomeEmail(mockData);

      expect(result.html).toContain("Chào mừng Nguyễn Văn A đến với NS Shop!");
      expect(result.html).toContain("<EMAIL>");
      expect(result.html).toContain("https://nsshop.com/auth/signin");
    });

    it("should include recipient name in text content", () => {
      const result = generateWelcomeEmail(mockData);

      expect(result.text).toContain("Chào mừng Nguyễn Văn A đến với NS Shop!");
      expect(result.text).toContain("https://nsshop.com/auth/signin");
      expect(result.text).toContain("<EMAIL>");
    });
  });

  describe("generateOrderConfirmationEmail", () => {
    const mockData: OrderConfirmationEmailData = {
      recipientName: "Nguyễn Văn A",
      recipientEmail: "<EMAIL>",
      order: {
        id: "order_123456789",
        total: 500000,
        status: "PENDING",
        createdAt: "2024-01-15T10:30:00Z",
        items: [
          {
            name: "Áo thun nam",
            quantity: 2,
            price: 200000,
            image: "https://example.com/image.jpg",
          },
          {
            name: "Quần jean",
            quantity: 1,
            price: 300000,
          },
        ],
        shippingAddress: {
          fullName: "Nguyễn Văn A",
          address: "123 Đường ABC",
          city: "Hà Nội",
          postalCode: "100000",
          phone: "0123456789",
        },
      },
      trackingUrl: "https://nsshop.com/orders/order_123456789",
    };

    it("should generate order confirmation email with correct subject", () => {
      const result = generateOrderConfirmationEmail(mockData);

      expect(result.subject).toBe("Xác nhận đơn hàng #23456789 - NS Shop");
    });

    it("should include order details in HTML content", () => {
      const result = generateOrderConfirmationEmail(mockData);

      expect(result.html).toContain("Xác nhận đơn hàng #23456789");
      expect(result.html).toContain("Nguyễn Văn A");
      expect(result.html).toContain("500.000 ₫");
      expect(result.html).toContain("Áo thun nam");
      expect(result.html).toContain("Quần jean");
      expect(result.html).toContain("123 Đường ABC");
    });

    it("should include tracking URL when provided", () => {
      const result = generateOrderConfirmationEmail(mockData);

      expect(result.html).toContain(
        "https://nsshop.com/orders/order_123456789"
      );
      expect(result.html).toContain("Theo dõi đơn hàng");
    });

    it("should format currency correctly", () => {
      const result = generateOrderConfirmationEmail(mockData);

      expect(result.html).toContain("500.000 ₫");
      expect(result.html).toContain("400.000 ₫"); // 2 * 200000
      expect(result.html).toContain("300.000 ₫");
    });
  });

  describe("generateContactFormEmail", () => {
    const mockData: ContactFormEmailData = {
      recipientName: "NS Shop Admin",
      recipientEmail: "<EMAIL>",
      senderName: "Nguyễn Văn A",
      senderEmail: "<EMAIL>",
      senderPhone: "0123456789",
      subject: "Hỏi về sản phẩm",
      message: "Tôi muốn hỏi về sản phẩm áo thun.",
      submittedAt: "2024-01-15T10:30:00Z",
    };

    it("should generate contact form email with correct subject", () => {
      const result = generateContactFormEmail(mockData);

      expect(result.subject).toBe(
        "[Liên hệ] Hỏi về sản phẩm - từ Nguyễn Văn A"
      );
    });

    it("should include sender information in HTML content", () => {
      const result = generateContactFormEmail(mockData);

      expect(result.html).toContain("Nguyễn Văn A");
      expect(result.html).toContain("<EMAIL>");
      expect(result.html).toContain("0123456789");
      expect(result.html).toContain("Hỏi về sản phẩm");
      expect(result.html).toContain("Tôi muốn hỏi về sản phẩm áo thun.");
    });

    it("should include reply button with correct mailto link", () => {
      const result = generateContactFormEmail(mockData);

      expect(result.html).toContain(
        "mailto:<EMAIL>?subject=Re: Hỏi%20về%20sản%20phẩm"
      );
    });
  });

  describe("generatePasswordResetEmail", () => {
    const mockData: PasswordResetEmailData = {
      recipientName: "Nguyễn Văn A",
      recipientEmail: "<EMAIL>",
      resetUrl: "https://nsshop.com/auth/reset-password?token=abc123",
      expiresAt: "2024-01-15T10:45:00Z",
    };

    it("should generate password reset email with correct subject", () => {
      const result = generatePasswordResetEmail(mockData);

      expect(result.subject).toBe("Đặt lại mật khẩu tài khoản NS Shop");
    });

    it("should include reset URL and expiry time", () => {
      const result = generatePasswordResetEmail(mockData);

      expect(result.html).toContain(
        "https://nsshop.com/auth/reset-password?token=abc123"
      );
      expect(result.html).toContain("15 phút");
      expect(result.text).toContain(
        "https://nsshop.com/auth/reset-password?token=abc123"
      );
    });

    it("should include security warning", () => {
      const result = generatePasswordResetEmail(mockData);

      expect(result.html).toContain("Bảo mật");
      expect(result.html).toContain("không yêu cầu đặt lại mật khẩu");
    });
  });

  describe("generateAdminNotificationEmail", () => {
    const mockData: AdminNotificationEmailData = {
      recipientName: "Admin User",
      recipientEmail: "<EMAIL>",
      notification: {
        id: "notif_123",
        title: "Đơn hàng mới",
        message: "Có đơn hàng mới cần xử lý",
        type: "INFO",
        priority: "HIGH",
        actionUrl: "https://nsshop.com/admin/orders/123",
        createdAt: "2024-01-15T10:30:00Z",
      },
      sender: {
        name: "System",
        email: "<EMAIL>",
      },
    };

    it("should generate admin notification email with correct subject", () => {
      const result = generateAdminNotificationEmail(mockData);

      expect(result.subject).toBe("[NS Shop Admin] Đơn hàng mới");
    });

    it("should include notification details with proper styling", () => {
      const result = generateAdminNotificationEmail(mockData);

      expect(result.html).toContain("Đơn hàng mới");
      expect(result.html).toContain("Có đơn hàng mới cần xử lý");
      expect(result.html).toContain("Thông tin");
      expect(result.html).toContain("Cao");
      expect(result.html).toContain("System");
    });

    it("should include action URL when provided", () => {
      const result = generateAdminNotificationEmail(mockData);

      expect(result.html).toContain("https://nsshop.com/admin/orders/123");
      expect(result.html).toContain("Xem chi tiết");
    });

    it("should handle notification without sender", () => {
      const dataWithoutSender = { ...mockData };
      delete dataWithoutSender.sender;

      const result = generateAdminNotificationEmail(dataWithoutSender);

      expect(result.html).not.toContain("Người gửi:");
      expect(result.text).not.toContain("Người gửi:");
    });

    it("should apply correct color styling based on notification type", () => {
      const errorNotification = {
        ...mockData,
        notification: {
          ...mockData.notification,
          type: "ERROR",
        },
      };

      const result = generateAdminNotificationEmail(errorNotification);

      expect(result.html).toContain("#ef4444"); // Error color
    });
  });
});
